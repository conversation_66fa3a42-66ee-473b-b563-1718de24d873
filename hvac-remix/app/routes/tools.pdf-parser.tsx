/**
 * FAZA 3A: PDF Parser Tool
 * Testing interface for PDF to JSON conversion service
 * Integrates with Financial Dashboard for invoice processing
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useActionData, useLoaderData, Form, useNavigation } from "@remix-run/react";
import { useState } from "react";
import {
  DocumentTextIcon,
  ArrowUpTrayIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  DocumentArrowDownIcon
} from "@heroicons/react/24/outline";

import { requireUserId } from "~/session.server";
import { PDFToJSONService, type PDFStructuredData } from "~/services/pdf-to-json.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  
  return json({
    userId,
    supportedFormats: ['.pdf'],
    maxFileSize: '10MB',
    features: [
      'Text extraction from PDF documents',
      'Invoice data recognition (Polish & English)',
      'Table and structure detection',
      'Metadata extraction',
      'AI-powered data parsing with Bielik V3/Gemma3'
    ]
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  const action = formData.get("action") as string;

  try {
    switch (action) {
      case "parsePDF": {
        const file = formData.get("pdfFile") as File;
        
        if (!file || file.size === 0) {
          return json({ error: "Please select a PDF file" }, { status: 400 });
        }

        if (!file.name.toLowerCase().endsWith('.pdf')) {
          return json({ error: "Only PDF files are supported" }, { status: 400 });
        }

        if (file.size > 10 * 1024 * 1024) { // 10MB limit
          return json({ error: "File size must be less than 10MB" }, { status: 400 });
        }

        // Save uploaded file temporarily
        const buffer = await file.arrayBuffer();
        const tempFilePath = `/tmp/pdf_${Date.now()}_${file.name}`;
        
        // In a real implementation, you would save the file to disk
        // For now, we'll simulate the parsing process
        
        const mockResult: PDFStructuredData = {
          metadata: {
            title: file.name,
            pageCount: 1,
            fileSize: file.size,
            language: 'auto'
          },
          pages: [{
            pageNumber: 1,
            text: `Mock extracted text from ${file.name}\n\nFAKTURA NR: 2024/001\nData wystawienia: 15.01.2024\nTermin płatności: 30.01.2024\nSuma do zapłaty: 1,234.56 PLN\n\nSprzedawca: HVAC Solutions Sp. z o.o.\nul. Testowa 123\n00-001 Warszawa\nNIP: 123-456-78-90`,
            layout: { width: 595, height: 842, rotation: 0 },
            elements: []
          }],
          extractedData: {
            invoiceData: {
              invoiceNumber: '2024/001',
              issueDate: new Date('2024-01-15'),
              dueDate: new Date('2024-01-30'),
              totalAmount: 1234.56,
              currency: 'PLN',
              supplier: {
                name: 'HVAC Solutions Sp. z o.o.',
                address: 'ul. Testowa 123, 00-001 Warszawa',
                taxId: '123-456-78-90'
              },
              lineItems: [
                {
                  description: 'Serwis klimatyzacji',
                  quantity: 1,
                  unitPrice: 1000.00,
                  totalPrice: 1000.00
                },
                {
                  description: 'Części zamienne',
                  quantity: 2,
                  unitPrice: 117.28,
                  totalPrice: 234.56
                }
              ]
            }
          },
          processingInfo: {
            processingTime: 1500,
            method: 'HYBRID',
            confidence: 0.92
          }
        };

        return json({ 
          success: true, 
          message: `Successfully parsed PDF: ${file.name}`,
          data: mockResult,
          fileName: file.name,
          fileSize: file.size
        });
      }

      default:
        return json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error: any) {
    console.error("PDF parser error:", error);
    return json({ error: error.message }, { status: 500 });
  }
};

export default function PDFParserPage() {
  const { supportedFormats, maxFileSize, features } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showResults, setShowResults] = useState(false);

  const isProcessing = navigation.state === "submitting";

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setShowResults(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatCurrency = (amount: number, currency: string = 'PLN') => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center gap-3">
              <DocumentTextIcon className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">PDF Parser Tool</h1>
                <p className="mt-2 text-gray-600">
                  Convert PDF documents to structured JSON data with AI-powered invoice recognition
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Upload Section */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Upload PDF Document</h2>
              
              <Form method="post" encType="multipart/form-data">
                <input type="hidden" name="action" value="parsePDF" />
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select PDF File
                    </label>
                    <input
                      type="file"
                      name="pdfFile"
                      accept=".pdf"
                      onChange={handleFileSelect}
                      className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Supported formats: {supportedFormats.join(', ')} • Max size: {maxFileSize}
                    </p>
                  </div>

                  {selectedFile && (
                    <div className="bg-gray-50 rounded-md p-3">
                      <div className="flex items-center gap-2">
                        <DocumentTextIcon className="h-5 w-5 text-gray-400" />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {selectedFile.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatFileSize(selectedFile.size)}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  <button
                    type="submit"
                    disabled={!selectedFile || isProcessing}
                    className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        <ArrowUpTrayIcon className="h-4 w-4" />
                        Parse PDF
                      </>
                    )}
                  </button>
                </div>
              </Form>

              {/* Features List */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Features</h3>
                <ul className="space-y-2">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-xs text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Results Section */}
          <div className="lg:col-span-2">
            {actionData?.error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-800">Error</p>
                    <p className="text-sm text-red-700 mt-1">{actionData.error}</p>
                  </div>
                </div>
              </div>
            )}

            {actionData?.success && actionData.data && (
              <div className="space-y-6">
                {/* Success Message */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex">
                    <CheckCircleIcon className="h-5 w-5 text-green-400" />
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">Success</p>
                      <p className="text-sm text-green-700 mt-1">{actionData.message}</p>
                    </div>
                  </div>
                </div>

                {/* Processing Info */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Processing Results</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-blue-600">{actionData.data.processingInfo.confidence.toFixed(1)}%</p>
                      <p className="text-sm text-gray-600">Confidence</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">{actionData.data.processingInfo.processingTime}ms</p>
                      <p className="text-sm text-gray-600">Processing Time</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-purple-600">{actionData.data.metadata.pageCount}</p>
                      <p className="text-sm text-gray-600">Pages</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-orange-600">{actionData.data.processingInfo.method}</p>
                      <p className="text-sm text-gray-600">Method</p>
                    </div>
                  </div>
                </div>

                {/* Invoice Data */}
                {actionData.data.extractedData.invoiceData && (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Extracted Invoice Data</h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Invoice Number</label>
                          <p className="mt-1 text-sm text-gray-900">{actionData.data.extractedData.invoiceData.invoiceNumber || 'Not found'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Total Amount</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {actionData.data.extractedData.invoiceData.totalAmount 
                              ? formatCurrency(actionData.data.extractedData.invoiceData.totalAmount, actionData.data.extractedData.invoiceData.currency)
                              : 'Not found'
                            }
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Issue Date</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {actionData.data.extractedData.invoiceData.issueDate 
                              ? new Date(actionData.data.extractedData.invoiceData.issueDate).toLocaleDateString('pl-PL')
                              : 'Not found'
                            }
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Due Date</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {actionData.data.extractedData.invoiceData.dueDate 
                              ? new Date(actionData.data.extractedData.invoiceData.dueDate).toLocaleDateString('pl-PL')
                              : 'Not found'
                            }
                          </p>
                        </div>
                      </div>

                      {actionData.data.extractedData.invoiceData.supplier && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Supplier</label>
                          <div className="mt-1 text-sm text-gray-900">
                            <p>{actionData.data.extractedData.invoiceData.supplier.name}</p>
                            {actionData.data.extractedData.invoiceData.supplier.address && (
                              <p className="text-gray-600">{actionData.data.extractedData.invoiceData.supplier.address}</p>
                            )}
                            {actionData.data.extractedData.invoiceData.supplier.taxId && (
                              <p className="text-gray-600">NIP: {actionData.data.extractedData.invoiceData.supplier.taxId}</p>
                            )}
                          </div>
                        </div>
                      )}

                      {actionData.data.extractedData.invoiceData.lineItems && actionData.data.extractedData.invoiceData.lineItems.length > 0 && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Line Items</label>
                          <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Qty</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Unit Price</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {actionData.data.extractedData.invoiceData.lineItems.map((item, index) => (
                                  <tr key={index}>
                                    <td className="px-3 py-2 text-sm text-gray-900">{item.description}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{item.quantity}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{formatCurrency(item.unitPrice)}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{formatCurrency(item.totalPrice)}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Raw Text */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Extracted Text</h3>
                    <button
                      onClick={() => setShowResults(!showResults)}
                      className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-700"
                    >
                      <EyeIcon className="h-4 w-4" />
                      {showResults ? 'Hide' : 'Show'} Raw Text
                    </button>
                  </div>
                  
                  {showResults && (
                    <div className="bg-gray-50 rounded-md p-4 max-h-96 overflow-y-auto">
                      <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                        {actionData.data.pages.map(page => page.text).join('\n')}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            )}

            {!actionData && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                <DocumentArrowDownIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No PDF processed yet</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Upload a PDF document to see the parsing results here.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
