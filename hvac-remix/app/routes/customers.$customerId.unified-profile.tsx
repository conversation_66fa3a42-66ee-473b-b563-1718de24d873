/**
 * 🎯 UNIFIED CUSTOMER PROFILE PAGE
 * 
 * Demonstration page showcasing the comprehensive customer profile system
 * that leverages the full power of relational database design for complete
 * customer data aggregation.
 * 
 * Features:
 * - Complete 360° customer view with all data sources
 * - Real-time synchronization with GoBackend-Kratos
 * - AI-powered insights and predictions
 * - Interactive timeline and activity tracking
 * - Responsive design with cosmic-level UX
 * 
 * Philosophy: "Harness the true power of relational databases for comprehensive customer intelligence"
 */

import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useFetcher, useNavigation } from "@remix-run/remix";
import { UnifiedCustomerProfileDashboard } from "~/components/unified-customer-profile-dashboard";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { 
  ArrowLeft, 
  Database, 
  Zap, 
  Brain, 
  TrendingUp, 
  Shield, 
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';

/**
 * 📊 Loader - Fetch customer profile data
 */
export async function loader({ params }: LoaderFunctionArgs) {
  try {
    const { customerId } = params;
    
    if (!customerId) {
      throw new Error("Customer ID is required");
    }

    console.log(`🎯 Loading unified profile page for customer: ${customerId}`);

    // Fetch unified customer profile
    const response = await fetch(`${process.env.BASE_URL || 'http://localhost:3000'}/api/unified-customer-profile/${customerId}`);
    const profileData = await response.json();

    if (!profileData.success) {
      throw new Error(profileData.error || 'Failed to fetch customer profile');
    }

    // Get additional metadata
    const metadata = {
      loadedAt: new Date().toISOString(),
      dataCompleteness: profileData.profile.dataCompleteness,
      totalDataSources: countDataSources(profileData.profile),
      systemStatus: 'operational',
      lastSync: profileData.profile.lastUpdated,
    };

    return json({
      customerId,
      profile: profileData.profile,
      metadata,
      success: true
    });

  } catch (error) {
    console.error("❌ Error loading unified customer profile page:", error);
    
    return json({
      error: error.message,
      success: false
    }, { status: 500 });
  }
}

/**
 * 🔄 Action - Handle profile actions
 */
export async function action({ params, request }: ActionFunctionArgs) {
  try {
    const { customerId } = params;
    const formData = await request.formData();
    const actionType = formData.get("actionType") as string;
    
    console.log(`🔄 Processing action '${actionType}' for customer: ${customerId}`);

    // Forward action to API
    const response = await fetch(`${process.env.BASE_URL || 'http://localhost:3000'}/api/unified-customer-profile/${customerId}`, {
      method: 'POST',
      body: formData
    });

    const result = await response.json();
    
    return json({
      success: result.success,
      message: result.message,
      actionType,
      timestamp: new Date().toISOString(),
      ...result
    });

  } catch (error) {
    console.error("❌ Error processing customer profile action:", error);
    
    return json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * 🎨 Main Component
 */
export default function UnifiedCustomerProfilePage() {
  const data = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const fetcher = useFetcher();
  const navigation = useNavigation();

  const isLoading = navigation.state === "loading";
  const isSubmitting = navigation.state === "submitting";

  // Handle action clicks from dashboard
  const handleAction = (action: string, actionData?: any) => {
    const formData = new FormData();
    formData.append("actionType", action);
    
    if (actionData) {
      formData.append("data", JSON.stringify(actionData));
    }
    
    fetcher.submit(formData, { method: "post" });
  };

  // Error state
  if (!data.success) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error Loading Customer Profile</AlertTitle>
            <AlertDescription>
              {data.error || 'An unexpected error occurred while loading the customer profile.'}
            </AlertDescription>
          </Alert>
          
          <div className="mt-6 text-center">
            <Button variant="outline" onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Unified Customer Profile</h1>
              <p className="text-muted-foreground">
                Complete 360° customer view powered by relational database aggregation
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="flex items-center space-x-1">
              <Database className="h-3 w-3" />
              <span>PostgreSQL Powered</span>
            </Badge>
            <Badge variant="outline" className="flex items-center space-x-1">
              <Brain className="h-3 w-3" />
              <span>AI Enhanced</span>
            </Badge>
            <Badge variant="outline" className="flex items-center space-x-1">
              <Zap className="h-3 w-3" />
              <span>Real-time</span>
            </Badge>
          </div>
        </div>

        {/* System Status */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Data Completeness</p>
                  <p className="text-2xl font-bold text-green-600">{data.metadata.dataCompleteness}%</p>
                </div>
                <Shield className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Data Sources</p>
                  <p className="text-2xl font-bold text-blue-600">{data.metadata.totalDataSources}</p>
                </div>
                <Database className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">System Status</p>
                  <p className="text-lg font-bold text-green-600 capitalize">{data.metadata.systemStatus}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Last Sync</p>
                  <p className="text-sm font-bold">
                    {new Date(data.metadata.lastSync).toLocaleTimeString()}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Feedback */}
      {actionData && (
        <div className="mb-6">
          <Alert variant={actionData.success ? "default" : "destructive"}>
            <Info className="h-4 w-4" />
            <AlertTitle>
              {actionData.success ? "Action Completed" : "Action Failed"}
            </AlertTitle>
            <AlertDescription>
              {actionData.message || (actionData.success ? 
                `Successfully processed ${actionData.actionType}` : 
                `Failed to process ${actionData.actionType}: ${actionData.error}`
              )}
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Loading Indicator */}
      {(isLoading || isSubmitting) && (
        <div className="mb-6">
          <Alert>
            <Zap className="h-4 w-4 animate-pulse" />
            <AlertTitle>Processing...</AlertTitle>
            <AlertDescription>
              {isLoading ? "Loading customer profile data..." : "Processing your request..."}
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Main Dashboard */}
      <UnifiedCustomerProfileDashboard
        customerId={data.customerId}
        onActionClick={handleAction}
        showActions={true}
        refreshInterval={300000} // 5 minutes
      />

      {/* Technical Information */}
      <div className="mt-8 pt-6 border-t">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>Technical Implementation</span>
            </CardTitle>
            <CardDescription>
              This unified customer profile demonstrates the power of relational database design
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-semibold text-sm">Database Features</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Optimized JOIN queries across multiple tables</li>
                  <li>• Parallel data fetching for performance</li>
                  <li>• Real-time synchronization with GoBackend-Kratos</li>
                  <li>• Connection pooling and caching</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold text-sm">Data Sources</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Customer basic information</li>
                  <li>• Email intelligence with AI analysis</li>
                  <li>• Equipment and service history</li>
                  <li>• Financial data and invoices</li>
                  <li>• Communication records</li>
                  <li>• Document attachments</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold text-sm">AI Integration</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Bielik V3 powered insights</li>
                  <li>• Churn prediction analytics</li>
                  <li>• Upsell opportunity identification</li>
                  <li>• Maintenance recommendations</li>
                  <li>• Sentiment analysis</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

/**
 * 📊 Helper function to count data sources
 */
function countDataSources(profile: any): number {
  let sources = 0;
  
  if (profile.basicInfo) sources++;
  if (profile.communications?.totalCommunications > 0) sources++;
  if (profile.equipment?.length > 0) sources++;
  if (profile.financial?.totalInvoices > 0) sources++;
  if (profile.documents?.totalDocuments > 0) sources++;
  if (profile.timeline?.length > 0) sources++;
  if (profile.emailIntelligence?.totalEmails > 0) sources++;
  if (profile.transcriptions?.totalTranscriptions > 0) sources++;
  
  return sources;
}
