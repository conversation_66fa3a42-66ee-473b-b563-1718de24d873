/**
 * FAZA 3A: Comprehensive Financial Dashboard
 * Integrates with existing dual-source email processing, OCR pipeline, and GoBackend-Kratos
 * Building on Enhanced Inventory & Financial Core with cosmic-level mobile UX
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useRevalidator } from "@remix-run/react";
import { useState, useEffect } from "react";
import {
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  BanknotesIcon,
  ChartBarIcon,
  BuildingOfficeIcon
} from "@heroicons/react/24/outline";

import { requireUserId } from "~/session.server";
import { FinancialDashboardService } from "~/services/financial-dashboard.server";
import type { FinancialKPIs, InvoiceProcessingStatus, AccountsReceivable, ProjectProfitability, SupplierFinancialAnalytics, FinancialAlert, CashFlowForecast } from "~/services/financial-dashboard.server";

type LoaderData = {
  kpis: FinancialKPIs | null;
  invoiceProcessing: InvoiceProcessingStatus | null;
  accountsReceivable: AccountsReceivable | null;
  projectProfitability: ProjectProfitability | null;
  supplierAnalytics: SupplierFinancialAnalytics | null;
  alerts: FinancialAlert[];
  cashFlowForecast: CashFlowForecast | null;
  dateRange: { from: Date; to: Date } | undefined;
  error?: string;
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  const url = new URL(request.url);

  // Get date range from query params
  const fromParam = url.searchParams.get("from");
  const toParam = url.searchParams.get("to");

  const dateRange = fromParam && toParam ? {
    from: new Date(fromParam),
    to: new Date(toParam)
  } : undefined;

  try {
    const [
      kpis,
      invoiceProcessing,
      accountsReceivable,
      projectProfitability,
      supplierAnalytics,
      alerts,
      cashFlowForecast
    ] = await Promise.all([
      FinancialDashboardService.getFinancialKPIs(userId, dateRange),
      FinancialDashboardService.getInvoiceProcessingStatus(userId),
      FinancialDashboardService.getAccountsReceivable(userId),
      FinancialDashboardService.getProjectProfitability(userId, dateRange),
      FinancialDashboardService.getSupplierFinancialAnalytics(userId, dateRange),
      FinancialDashboardService.generateFinancialAlerts(userId),
      FinancialDashboardService.generateCashFlowForecast(userId)
    ]);

    return json<LoaderData>({
      kpis,
      invoiceProcessing,
      accountsReceivable,
      projectProfitability,
      supplierAnalytics,
      alerts,
      cashFlowForecast,
      dateRange
    });
  } catch (error) {
    console.error("Error loading financial dashboard:", error);
    return json<LoaderData>({
      kpis: null,
      invoiceProcessing: null,
      accountsReceivable: null,
      projectProfitability: null,
      supplierAnalytics: null,
      alerts: [],
      cashFlowForecast: null,
      dateRange,
      error: "Failed to load financial data"
    });
  }
};

export default function FinancialDashboard() {
  const data = useLoaderData<LoaderData>(); // Use the explicit type here
  const revalidator = useRevalidator();
  const [selectedTab, setSelectedTab] = useState<'overview' | 'invoices' | 'receivables' | 'projects' | 'suppliers'>('overview');

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      revalidator.revalidate();
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [revalidator]);

  if (data.error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error Loading Dashboard</h3>
          <p className="mt-1 text-sm text-gray-500">{data.error}</p>
        </div>
      </div>
    );
  }

  const { kpis, invoiceProcessing, accountsReceivable, projectProfitability, supplierAnalytics, alerts, cashFlowForecast } = data;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'HIGH':
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-500" />;
      case 'MEDIUM':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <CheckCircleIcon className="h-5 w-5 text-blue-500" />;
    }
  };

  const getAlertBadgeColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Financial Dashboard</h1>
                <p className="mt-2 text-gray-600">
                  Real-time financial insights and analytics for your HVAC business
                </p>
              </div>
              <div className="flex items-center gap-4">
                <div className="text-sm text-gray-500">
                  Last updated: {new Date().toLocaleTimeString()}
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-green-600">Live</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts Banner */}
      {alerts && alerts.length > 0 && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {alerts.length} Financial Alert{alerts.length > 1 ? 's' : ''} Require Attention
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc list-inside space-y-1">
                  {alerts.slice(0, 3).map((alert) => (
                    <li key={alert.id}>
                      {alert.title}: {alert.description}
                      {alert.amount && ` (${formatCurrency(alert.amount)})`}
                    </li>
                  ))}
                  {alerts.length > 3 && (
                    <li>And {alerts.length - 3} more alerts...</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* KPI Cards */}
        {kpis && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Total Revenue */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(kpis.totalRevenue)}</p>
                  <div className="flex items-center mt-1">
                    {kpis.monthlyGrowth >= 0 ? (
                      <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                    ) : (
                      <ArrowTrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
                    )}
                    <span className={`text-sm ${kpis.monthlyGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatPercentage(Math.abs(kpis.monthlyGrowth))} vs last month
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Profit Margin */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Profit Margin</p>
                  <p className="text-2xl font-bold text-gray-900">{formatPercentage(kpis.profitMargin)}</p>
                  <p className="text-sm text-gray-600 mt-1">
                    Gross Profit: {formatCurrency(kpis.grossProfit)}
                  </p>
                </div>
              </div>
            </div>

            {/* Cash Flow */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BanknotesIcon className={`h-8 w-8 ${kpis.cashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Cash Flow</p>
                  <p className={`text-2xl font-bold ${kpis.cashFlow >= 0 ? 'text-green-900' : 'text-red-900'}`}>
                    {formatCurrency(kpis.cashFlow)}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    A/R: {formatCurrency(kpis.accountsReceivable)}
                  </p>
                </div>
              </div>
            </div>

            {/* Outstanding Invoices */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentTextIcon className="h-8 w-8 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Outstanding Invoices</p>
                  <p className="text-2xl font-bold text-gray-900">{kpis.outstandingInvoices}</p>
                  <p className="text-sm text-gray-600 mt-1">
                    Avg. {kpis.daysToPayment.toFixed(0)} days to payment
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Overview', icon: ChartBarIcon },
              { id: 'invoices', name: 'Invoice Processing', icon: DocumentTextIcon },
              { id: 'receivables', name: 'Receivables', icon: CurrencyDollarIcon },
              { id: 'projects', name: 'Project Profitability', icon: BuildingOfficeIcon },
              { id: 'suppliers', name: 'Supplier Analytics', icon: ArrowTrendingUpIcon }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setSelectedTab(tab.id as any)}
                  className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    selectedTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {selectedTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Cash Flow Forecast */}
              {cashFlowForecast && (
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Cash Flow Forecast (90 Days)</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Current Cash Position</span>
                      <span className="font-medium">{formatCurrency(cashFlowForecast.currentCash)}</span>
                    </div>

                    {cashFlowForecast.riskFactors.length > 0 && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                        <h4 className="text-sm font-medium text-yellow-800 mb-2">Risk Factors</h4>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          {cashFlowForecast.riskFactors.map((risk, index) => (
                            <li key={index}>• {risk}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {cashFlowForecast.recommendations.length > 0 && (
                      <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                        <h4 className="text-sm font-medium text-blue-800 mb-2">Recommendations</h4>
                        <ul className="text-sm text-blue-700 space-y-1">
                          {cashFlowForecast.recommendations.map((rec, index) => (
                            <li key={index}>• {rec}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Recent Alerts */}
              <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Financial Alerts</h3>
                <div className="space-y-3">
                  {alerts && alerts.length > 0 ? (
                    alerts.slice(0, 5).map((alert) => (
                      <div key={alert.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-md">
                        {getAlertIcon(alert.severity)}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="text-sm font-medium text-gray-900">{alert.title}</p>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAlertBadgeColor(alert.severity)}`}>
                              {alert.severity}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600">{alert.description}</p>
                          {alert.amount && (
                            <p className="text-sm font-medium text-gray-900 mt-1">
                              Amount: {formatCurrency(alert.amount)}
                            </p>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <CheckCircleIcon className="mx-auto h-12 w-12 text-green-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No Active Alerts</h3>
                      <p className="mt-1 text-sm text-gray-500">Your financial metrics look healthy!</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Invoice Processing Tab */}
          {selectedTab === 'invoices' && invoiceProcessing && (
            <div className="space-y-6">
              {/* Processing Status Overview */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{invoiceProcessing.totalProcessed}</p>
                    <p className="text-sm text-gray-600">Total Processed</p>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{invoiceProcessing.successfulExtractions}</p>
                    <p className="text-sm text-gray-600">Successful</p>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-red-600">{invoiceProcessing.failedExtractions}</p>
                    <p className="text-sm text-gray-600">Failed</p>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{formatPercentage(invoiceProcessing.accuracyRate)}</p>
                    <p className="text-sm text-gray-600">Accuracy Rate</p>
                  </div>
                </div>
              </div>

              {/* Recent Processing */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Recent Invoice Processing</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          File Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Extracted Amount
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Confidence
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Processed At
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {invoiceProcessing.recentProcessing.map((item) => (
                        <tr key={item.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.fileName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                              item.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                              item.status === 'FAILED' ? 'bg-red-100 text-red-800' :
                              item.status === 'PROCESSING' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {item.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.extractedAmount ? formatCurrency(item.extractedAmount) : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatPercentage(item.confidence)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(item.processedAt).toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Accounts Receivable Tab */}
          {selectedTab === 'receivables' && accountsReceivable && (
            <div className="space-y-6">
              {/* A/R Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Total Outstanding</h3>
                  <p className="text-3xl font-bold text-gray-900">{formatCurrency(accountsReceivable.totalOutstanding)}</p>
                  <div className="mt-2 flex items-center">
                    <span className="text-sm text-red-600">Overdue: {formatCurrency(accountsReceivable.overdueAmount)}</span>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Aging Analysis</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Current</span>
                      <span className="text-sm font-medium">{formatCurrency(accountsReceivable.aging.current)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">1-30 days</span>
                      <span className="text-sm font-medium">{formatCurrency(accountsReceivable.aging.days30)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">31-60 days</span>
                      <span className="text-sm font-medium">{formatCurrency(accountsReceivable.aging.days60)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">61-90 days</span>
                      <span className="text-sm font-medium">{formatCurrency(accountsReceivable.aging.days90)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">90+ days</span>
                      <span className="text-sm font-medium text-red-600">{formatCurrency(accountsReceivable.aging.over90)}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Collection Metrics</h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-600">Collection Rate</p>
                      <p className="text-2xl font-bold text-green-600">
                        {formatPercentage((accountsReceivable.currentAmount / accountsReceivable.totalOutstanding) * 100)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Top Customers</p>
                      <p className="text-lg font-medium">{accountsReceivable.topCustomers.length}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Top Customers */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Top Customers by Outstanding Amount</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Customer
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Outstanding
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Overdue
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Last Payment
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {accountsReceivable.topCustomers.map((customer) => (
                        <tr key={customer.customerId} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {customer.customerName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(customer.outstandingAmount)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                            {formatCurrency(customer.overdueAmount)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {customer.lastPaymentDate ? new Date(customer.lastPaymentDate).toLocaleDateString() : 'Never'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Project Profitability Tab */}
          {selectedTab === 'projects' && projectProfitability && (
            <div className="space-y-6">
              {/* Project Overview */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{projectProfitability.totalProjects}</p>
                    <p className="text-sm text-gray-600">Total Projects</p>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{formatCurrency(projectProfitability.totalRevenue)}</p>
                    <p className="text-sm text-gray-600">Total Revenue</p>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-red-600">{formatCurrency(projectProfitability.totalCosts)}</p>
                    <p className="text-sm text-gray-600">Total Costs</p>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{formatPercentage(projectProfitability.averageMargin)}</p>
                    <p className="text-sm text-gray-600">Average Margin</p>
                  </div>
                </div>
              </div>

              {/* Project Details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Project Profitability Analysis</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Project
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Revenue
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Costs
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Margin
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Margin %
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {projectProfitability.projects.map((project) => (
                        <tr key={project.projectId} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {project.projectName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(project.revenue)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(project.costs)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(project.margin)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`text-sm font-medium ${
                              project.marginPercentage >= 20 ? 'text-green-600' :
                              project.marginPercentage >= 10 ? 'text-yellow-600' :
                              'text-red-600'
                            }`}>
                              {formatPercentage(project.marginPercentage)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                              project.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                              project.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {project.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Supplier Analytics Tab */}
          {selectedTab === 'suppliers' && supplierAnalytics && (
            <div className="space-y-6">
              {/* Supplier Overview */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{supplierAnalytics.totalSuppliers}</p>
                    <p className="text-sm text-gray-600">Active Suppliers</p>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{supplierAnalytics.totalPayments}</p>
                    <p className="text-sm text-gray-600">Total Orders</p>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{supplierAnalytics.averagePaymentDays.toFixed(0)}</p>
                    <p className="text-sm text-gray-600">Avg Payment Days</p>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{formatPercentage(supplierAnalytics.onTimePaymentRate)}</p>
                    <p className="text-sm text-gray-600">On-Time Rate</p>
                  </div>
                </div>
              </div>

              {/* Supplier Details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Supplier Performance Analysis</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Supplier
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total Spent
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Orders
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Avg Payment Days
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Compliance Rate
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Last Payment
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {supplierAnalytics.suppliers.map((supplier) => (
                        <tr key={supplier.supplierId} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {supplier.supplierName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(supplier.totalSpent)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {supplier.totalPayments}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {supplier.averagePaymentDays.toFixed(0)} days
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`text-sm font-medium ${
                              supplier.paymentTermsCompliance >= 90 ? 'text-green-600' :
                              supplier.paymentTermsCompliance >= 70 ? 'text-yellow-600' :
                              'text-red-600'
                            }`}>
                              {formatPercentage(supplier.paymentTermsCompliance)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {supplier.lastPaymentDate ? new Date(supplier.lastPaymentDate).toLocaleDateString() : 'Never'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
