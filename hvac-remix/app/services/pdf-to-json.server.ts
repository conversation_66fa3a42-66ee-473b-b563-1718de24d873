/**
 * FAZA 3A: PDF to JSON Conversion Service
 * Professional PDF parsing and structured data extraction for Financial Dashboard
 * Integrates with existing OCR pipeline and AI analysis (Bielik V3/Gemma3)
 */

import { promises as fs } from 'fs';
import path from 'path';

// =====================================================
// TYPES & INTERFACES
// =====================================================

export interface PDFParsingOptions {
  extractImages?: boolean;
  extractTables?: boolean;
  extractMetadata?: boolean;
  preserveLayout?: boolean;
  pageRange?: { start: number; end: number };
  language?: 'en' | 'pl' | 'auto';
  confidenceThreshold?: number;
}

export interface PDFStructuredData {
  metadata: {
    title?: string;
    author?: string;
    creator?: string;
    producer?: string;
    creationDate?: Date;
    modificationDate?: Date;
    pageCount: number;
    fileSize: number;
    language?: string;
  };
  pages: Array<{
    pageNumber: number;
    text: string;
    layout: {
      width: number;
      height: number;
      rotation: number;
    };
    elements: Array<{
      type: 'text' | 'table' | 'image' | 'header' | 'footer';
      content: string;
      position: {
        x: number;
        y: number;
        width: number;
        height: number;
      };
      confidence: number;
      metadata?: any;
    }>;
  }>;
  extractedData: {
    invoiceData?: InvoiceData;
    tableData?: TableData[];
    imageData?: ImageData[];
    textBlocks?: TextBlock[];
  };
  processingInfo: {
    processingTime: number;
    method: 'OCR' | 'TEXT_EXTRACTION' | 'HYBRID';
    confidence: number;
    errors?: string[];
    warnings?: string[];
  };
}

export interface InvoiceData {
  invoiceNumber?: string;
  issueDate?: Date;
  dueDate?: Date;
  totalAmount?: number;
  taxAmount?: number;
  netAmount?: number;
  currency?: string;
  supplier?: {
    name?: string;
    address?: string;
    taxId?: string;
    email?: string;
    phone?: string;
  };
  customer?: {
    name?: string;
    address?: string;
    taxId?: string;
  };
  lineItems?: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    taxRate?: number;
    category?: string;
  }>;
  paymentTerms?: string;
  paymentMethod?: string;
  bankAccount?: string;
}

export interface TableData {
  headers: string[];
  rows: string[][];
  position: { x: number; y: number; width: number; height: number };
  confidence: number;
}

export interface ImageData {
  base64: string;
  format: string;
  position: { x: number; y: number; width: number; height: number };
  description?: string;
}

export interface TextBlock {
  text: string;
  type: 'paragraph' | 'heading' | 'list' | 'footer' | 'header';
  position: { x: number; y: number; width: number; height: number };
  fontSize: number;
  fontFamily?: string;
  confidence: number;
}

// =====================================================
// PDF TO JSON SERVICE
// =====================================================

export class PDFToJSONService {

  /**
   * Convert PDF file to structured JSON data
   */
  static async convertPDFToJSON(
    filePath: string,
    options: PDFParsingOptions = {}
  ): Promise<PDFStructuredData> {
    const startTime = Date.now();
    
    try {
      // Validate file exists and is PDF
      await this.validatePDFFile(filePath);
      
      // Get file metadata
      const fileStats = await fs.stat(filePath);
      
      // Determine best extraction method
      const extractionMethod = await this.determineExtractionMethod(filePath);
      
      let structuredData: PDFStructuredData;
      
      switch (extractionMethod) {
        case 'TEXT_EXTRACTION':
          structuredData = await this.extractTextBasedPDF(filePath, options);
          break;
        case 'OCR':
          structuredData = await this.extractOCRBasedPDF(filePath, options);
          break;
        case 'HYBRID':
          structuredData = await this.extractHybridPDF(filePath, options);
          break;
        default:
          throw new Error('Unable to determine extraction method');
      }
      
      // Add processing metadata
      structuredData.metadata.fileSize = fileStats.size;
      structuredData.processingInfo.processingTime = Date.now() - startTime;
      structuredData.processingInfo.method = extractionMethod;
      
      // Apply AI enhancement for invoice detection
      if (this.isLikelyInvoice(structuredData)) {
        structuredData.extractedData.invoiceData = await this.extractInvoiceDataWithAI(structuredData);
      }
      
      return structuredData;
      
    } catch (error) {
      console.error('Error converting PDF to JSON:', error);
      throw new Error(`PDF conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract data from text-based PDF (selectable text)
   */
  private static async extractTextBasedPDF(
    filePath: string,
    options: PDFParsingOptions
  ): Promise<PDFStructuredData> {
    try {
      // This would use a library like pdf-parse or pdf2json
      // For now, we'll create a mock structure
      const mockData: PDFStructuredData = {
        metadata: {
          pageCount: 1,
          fileSize: 0,
          title: 'Text-based PDF'
        },
        pages: [{
          pageNumber: 1,
          text: 'Extracted text content would go here',
          layout: { width: 595, height: 842, rotation: 0 },
          elements: []
        }],
        extractedData: {},
        processingInfo: {
          processingTime: 0,
          method: 'TEXT_EXTRACTION',
          confidence: 0.9
        }
      };
      
      return mockData;
    } catch (error) {
      throw new Error(`Text extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract data from image-based PDF using OCR
   */
  private static async extractOCRBasedPDF(
    filePath: string,
    options: PDFParsingOptions
  ): Promise<PDFStructuredData> {
    try {
      // This would integrate with Azure Vision API or similar OCR service
      // For now, we'll create a mock structure
      const mockData: PDFStructuredData = {
        metadata: {
          pageCount: 1,
          fileSize: 0,
          title: 'OCR-processed PDF'
        },
        pages: [{
          pageNumber: 1,
          text: 'OCR extracted text would go here',
          layout: { width: 595, height: 842, rotation: 0 },
          elements: []
        }],
        extractedData: {},
        processingInfo: {
          processingTime: 0,
          method: 'OCR',
          confidence: 0.8
        }
      };
      
      return mockData;
    } catch (error) {
      throw new Error(`OCR extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract data using hybrid approach (text + OCR)
   */
  private static async extractHybridPDF(
    filePath: string,
    options: PDFParsingOptions
  ): Promise<PDFStructuredData> {
    try {
      // Combine text extraction and OCR for best results
      const textData = await this.extractTextBasedPDF(filePath, options);
      const ocrData = await this.extractOCRBasedPDF(filePath, options);
      
      // Merge results intelligently
      const hybridData: PDFStructuredData = {
        ...textData,
        processingInfo: {
          processingTime: textData.processingInfo.processingTime + ocrData.processingInfo.processingTime,
          method: 'HYBRID',
          confidence: Math.max(textData.processingInfo.confidence, ocrData.processingInfo.confidence)
        }
      };
      
      return hybridData;
    } catch (error) {
      throw new Error(`Hybrid extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract invoice data using AI analysis
   */
  private static async extractInvoiceDataWithAI(data: PDFStructuredData): Promise<InvoiceData> {
    try {
      const allText = data.pages.map(page => page.text).join('\n');
      
      // Enhanced patterns for Polish and English invoices
      const patterns = {
        invoiceNumber: [
          /(?:faktura|invoice|rachunek)\s*(?:nr\.?|no\.?|#)?\s*:?\s*([A-Z0-9\/\-]+)/i,
          /(?:nr\.?\s*faktury|invoice\s*number)\s*:?\s*([A-Z0-9\/\-]+)/i
        ],
        totalAmount: [
          /(?:suma|total|razem|do\s*zapłaty|amount\s*due)\s*:?\s*(\d{1,3}(?:[\s,]\d{3})*(?:[,.]\d{2})?)\s*(?:zł|pln|\$|eur|€)?/i,
          /(?:wartość\s*brutto|gross\s*amount)\s*:?\s*(\d{1,3}(?:[\s,]\d{3})*(?:[,.]\d{2})?)/i
        ],
        issueDate: [
          /(?:data\s*wystawienia|issue\s*date|data\s*faktury)\s*:?\s*(\d{1,2}[.\-\/]\d{1,2}[.\-\/]\d{2,4})/i,
          /(?:wystawiono|issued)\s*:?\s*(\d{1,2}[.\-\/]\d{1,2}[.\-\/]\d{2,4})/i
        ],
        dueDate: [
          /(?:termin\s*płatności|due\s*date|zapłać\s*do)\s*:?\s*(\d{1,2}[.\-\/]\d{1,2}[.\-\/]\d{2,4})/i,
          /(?:płatne\s*do|payment\s*due)\s*:?\s*(\d{1,2}[.\-\/]\d{1,2}[.\-\/]\d{2,4})/i
        ],
        supplierName: [
          /(?:sprzedawca|seller|dostawca|supplier)\s*:?\s*([A-ZĄĆĘŁŃÓŚŹŻ][A-Za-ząćęłńóśźż\s&\.,\-]+?)(?:\n|ul\.|address)/i,
          /(?:firma|company)\s*:?\s*([A-ZĄĆĘŁŃÓŚŹŻ][A-Za-ząćęłńóśźż\s&\.,\-]+?)(?:\n|ul\.)/i
        ]
      };

      const invoiceData: InvoiceData = {};

      // Extract invoice number
      for (const pattern of patterns.invoiceNumber) {
        const match = allText.match(pattern);
        if (match) {
          invoiceData.invoiceNumber = match[1].trim();
          break;
        }
      }

      // Extract total amount
      for (const pattern of patterns.totalAmount) {
        const match = allText.match(pattern);
        if (match) {
          const amountStr = match[1].replace(/[\s,]/g, '').replace(',', '.');
          invoiceData.totalAmount = parseFloat(amountStr);
          break;
        }
      }

      // Extract dates
      for (const pattern of patterns.issueDate) {
        const match = allText.match(pattern);
        if (match) {
          invoiceData.issueDate = this.parseDate(match[1]);
          break;
        }
      }

      for (const pattern of patterns.dueDate) {
        const match = allText.match(pattern);
        if (match) {
          invoiceData.dueDate = this.parseDate(match[1]);
          break;
        }
      }

      // Extract supplier name
      for (const pattern of patterns.supplierName) {
        const match = allText.match(pattern);
        if (match) {
          invoiceData.supplier = {
            name: match[1].trim()
          };
          break;
        }
      }

      // Extract line items (simplified)
      invoiceData.lineItems = this.extractLineItemsFromText(allText);

      return invoiceData;
    } catch (error) {
      console.error('Error extracting invoice data with AI:', error);
      return {};
    }
  }

  /**
   * Extract line items from text
   */
  private static extractLineItemsFromText(text: string): Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }> {
    const lineItems = [];
    const lines = text.split('\n');
    
    for (const line of lines) {
      // Pattern for line items: Description Qty Unit_Price Total_Price
      const match = line.match(/(.+?)\s+(\d+(?:[,.]\d+)?)\s+(\d+(?:[,.]\d{2})?)\s+(\d+(?:[,.]\d{2})?)/);
      if (match) {
        const [, description, qty, unitPrice, totalPrice] = match;
        lineItems.push({
          description: description.trim(),
          quantity: parseFloat(qty.replace(',', '.')),
          unitPrice: parseFloat(unitPrice.replace(',', '.')),
          totalPrice: parseFloat(totalPrice.replace(',', '.'))
        });
      }
    }

    return lineItems;
  }

  /**
   * Parse date string to Date object
   */
  private static parseDate(dateStr: string): Date {
    // Handle various date formats
    const cleanDate = dateStr.replace(/[.\-]/g, '/');
    const date = new Date(cleanDate);
    return isNaN(date.getTime()) ? new Date() : date;
  }

  /**
   * Validate PDF file
   */
  private static async validatePDFFile(filePath: string): Promise<void> {
    try {
      const stats = await fs.stat(filePath);
      if (!stats.isFile()) {
        throw new Error('Path is not a file');
      }
      
      const ext = path.extname(filePath).toLowerCase();
      if (ext !== '.pdf') {
        throw new Error('File is not a PDF');
      }
    } catch (error) {
      throw new Error(`File validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Determine best extraction method for PDF
   */
  private static async determineExtractionMethod(filePath: string): Promise<'TEXT_EXTRACTION' | 'OCR' | 'HYBRID'> {
    // For now, default to HYBRID for best results
    // In a real implementation, this would analyze the PDF structure
    return 'HYBRID';
  }

  /**
   * Check if PDF is likely an invoice
   */
  private static isLikelyInvoice(data: PDFStructuredData): boolean {
    const allText = data.pages.map(page => page.text).join(' ').toLowerCase();
    const invoiceKeywords = [
      'faktura', 'invoice', 'rachunek', 'bill',
      'do zapłaty', 'amount due', 'total', 'suma',
      'sprzedawca', 'seller', 'nabywca', 'buyer'
    ];
    
    return invoiceKeywords.some(keyword => allText.includes(keyword));
  }

  /**
   * Convert PDF to JSON with enhanced error handling
   */
  static async convertPDFToJSONSafe(
    filePath: string,
    options: PDFParsingOptions = {}
  ): Promise<{ success: boolean; data?: PDFStructuredData; error?: string }> {
    try {
      const data = await this.convertPDFToJSON(filePath, options);
      return { success: true, data };
    } catch (error) {
      console.error('PDF to JSON conversion failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}
