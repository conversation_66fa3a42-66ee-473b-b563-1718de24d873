/**
 * 🎯 UNIFIED CUSTOMER PROFILE SERVICE
 *
 * Comprehensive customer data aggregation service that leverages the full power
 * of PostgreSQL relational database design to create unified customer profiles.
 *
 * Features:
 * - Efficient JOIN queries across all customer-related tables
 * - Real-time data synchronization with GoBackend-Kratos
 * - Performance optimization with connection pooling and caching
 * - Complete 360° customer view aggregation
 *
 * Philosophy: "Harness the true power of relational databases for comprehensive customer intelligence"
 */

import { prisma } from '~/db.server';
import { goBackendBridge } from './gobackend-bridge.server';
import { bielikService } from './bielik.server';

// 🎯 Comprehensive Customer Profile Interface
export interface UnifiedCustomerProfile {
  // Basic Information
  customerId: string;
  basicInfo: CustomerBasicInfo;
  contactInfo: CustomerContactInfo;

  // Analytics & Metrics
  analytics: CustomerAnalytics;
  healthScore: CustomerHealthScore;
  lifecycleStage: CustomerLifecycleStage;

  // Communication & Interactions
  communications: CommunicationSummary;
  emailIntelligence: EmailIntelligenceSummary;
  transcriptions: TranscriptionSummary;

  // Equipment & Services
  equipment: EquipmentSummary[];
  serviceHistory: ServiceHistorySummary;

  // Financial Information
  financial: FinancialSummary;

  // Documents & Attachments
  documents: DocumentSummary;

  // Timeline & Activity
  timeline: CustomerTimelineEvent[];
  recentActivity: RecentActivitySummary;

  // AI Insights & Predictions
  aiInsights: CustomerAIInsights;

  // Metadata
  lastUpdated: Date;
  dataCompleteness: number; // 0-100%
}

// 📊 Supporting Interfaces
export interface CustomerBasicInfo {
  name: string;
  type: 'RESIDENTIAL' | 'COMMERCIAL' | 'INDUSTRIAL';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  accountNumber: string;
  taxId?: string;
  website?: string;
  industry?: string;
  companySize?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CustomerContactInfo {
  primaryAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    coordinates?: { lat: number; lng: number };
  };
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  primaryPhone: string;
  secondaryPhone?: string;
  email: string;
  alternateEmail?: string;
  preferredContactMethod: 'EMAIL' | 'PHONE' | 'SMS';
  businessHours?: Record<string, { open: string; close: string }>;
}

export interface CustomerAnalytics {
  totalServiceOrders: number;
  totalRevenue: number;
  averageJobValue: number;
  customerLifetimeValue: number;
  lastServiceDate?: Date;
  nextScheduledService?: Date;
  satisfactionScore?: number;
  loyaltyTier: 'BRONZE' | 'SILVER' | 'GOLD' | 'PLATINUM';
  churnRisk: number; // 0-100%
  acquisitionDate: Date;
  monthsAsCustomer: number;
}

export interface CustomerHealthScore {
  overall: number; // 0-100
  serviceFrequency: number;
  paymentHistory: number;
  communicationResponsiveness: number;
  contractCompliance: number;
  referralActivity: number;
  lastCalculated: Date;
}

export interface CustomerLifecycleStage {
  current: 'LEAD' | 'PROSPECT' | 'NEW_CUSTOMER' | 'ACTIVE_CUSTOMER' | 'LOYAL_CUSTOMER' | 'AT_RISK' | 'CHURNED' | 'REACTIVATED';
  previousStage?: string;
  stageChangedAt: Date;
  daysInCurrentStage: number;
}

export interface CommunicationSummary {
  totalCommunications: number;
  emailCount: number;
  phoneCallCount: number;
  smsCount: number;
  inPersonCount: number;
  lastCommunicationDate?: Date;
  averageResponseTime: number; // hours
  communicationFrequency: number; // per month
  preferredChannel: string;
  sentimentTrend: 'IMPROVING' | 'STABLE' | 'DECLINING';
}

export interface EmailIntelligenceSummary {
  totalEmails: number;
  inboundEmails: number;
  outboundEmails: number;
  averageSentiment: number; // -1 to 1
  urgentEmails: number;
  unreadEmails: number;
  lastEmailDate?: Date;
  topCategories: Array<{ category: string; count: number }>;
  aiConfidenceAverage: number;
}

export interface TranscriptionSummary {
  totalTranscriptions: number;
  totalCallDuration: number; // minutes
  averageCallDuration: number;
  lastCallDate?: Date;
  callFrequency: number; // per month
  topTopics: Array<{ topic: string; frequency: number }>;
  sentimentTrend: Array<{ date: Date; sentiment: number }>;
}

export interface EquipmentSummary {
  id: string;
  type: string;
  brand: string;
  model: string;
  serialNumber: string;
  installationDate?: Date;
  warrantyExpiry?: Date;
  lastServiceDate?: Date;
  nextServiceDue?: Date;
  status: 'ACTIVE' | 'INACTIVE' | 'NEEDS_REPLACEMENT';
  maintenanceCompliance: number; // 0-100%
  serviceCount: number;
  totalServiceCost: number;
}

export interface ServiceHistorySummary {
  totalServices: number;
  maintenanceServices: number;
  repairServices: number;
  installationServices: number;
  emergencyServices: number;
  averageServiceCost: number;
  totalServiceCost: number;
  onTimePercentage: number;
  customerSatisfactionAverage: number;
  preferredTechnician?: string;
  lastServiceDate?: Date;
  upcomingServices: Array<{
    id: string;
    scheduledDate: Date;
    type: string;
    equipment: string;
  }>;
}

export interface FinancialSummary {
  totalRevenue: number;
  totalInvoices: number;
  paidInvoices: number;
  pendingInvoices: number;
  overdueInvoices: number;
  averagePaymentTime: number; // days
  creditLimit?: number;
  currentBalance: number;
  lastPaymentDate?: Date;
  paymentMethods: Array<{ method: string; frequency: number }>;
  monthlyRevenueTrend: Array<{ month: string; revenue: number }>;
}

export interface DocumentSummary {
  totalDocuments: number;
  contracts: number;
  invoices: number;
  serviceReports: number;
  warranties: number;
  photos: number;
  other: number;
  lastDocumentAdded?: Date;
  storageUsed: number; // MB
}

export interface CustomerTimelineEvent {
  id: string;
  type: 'SERVICE' | 'COMMUNICATION' | 'PAYMENT' | 'EQUIPMENT' | 'DOCUMENT' | 'SYSTEM';
  title: string;
  description: string;
  timestamp: Date;
  relatedEntityId?: string;
  relatedEntityType?: string;
  importance: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  metadata?: Record<string, any>;
}

export interface RecentActivitySummary {
  lastLogin?: Date;
  lastServiceRequest?: Date;
  lastPayment?: Date;
  lastCommunication?: Date;
  recentChanges: Array<{
    field: string;
    oldValue: any;
    newValue: any;
    changedAt: Date;
    changedBy: string;
  }>;
}

export interface CustomerAIInsights {
  churnPrediction: {
    probability: number;
    factors: string[];
    recommendation: string;
  };
  upsellOpportunities: Array<{
    service: string;
    probability: number;
    estimatedValue: number;
    reasoning: string;
  }>;
  maintenanceRecommendations: Array<{
    equipment: string;
    recommendation: string;
    urgency: 'LOW' | 'MEDIUM' | 'HIGH';
    estimatedCost: number;
  }>;
  communicationInsights: {
    preferredTime: string;
    responsePattern: string;
    sentimentAnalysis: string;
  };
  lastAnalyzed: Date;
}

/**
 * 🚀 UNIFIED CUSTOMER PROFILE SERVICE
 *
 * Main service class that orchestrates comprehensive customer data aggregation
 * using optimized database queries and real-time synchronization.
 */
export class UnifiedCustomerProfileService {

  /**
   * Get comprehensive unified customer profile
   * Uses optimized JOIN queries to aggregate all customer data
   */
  static async getUnifiedProfile(customerId: string): Promise<UnifiedCustomerProfile | null> {
    try {
      console.log(`🎯 Fetching unified profile for customer: ${customerId}`);

      // Execute parallel queries for optimal performance
      const [
        basicData,
        communicationData,
        equipmentData,
        financialData,
        analyticsData,
        timelineData
      ] = await Promise.all([
        this.getCustomerBasicData(customerId),
        this.getCommunicationData(customerId),
        this.getEquipmentData(customerId),
        this.getFinancialData(customerId),
        this.getAnalyticsData(customerId),
        this.getTimelineData(customerId)
      ]);

      if (!basicData) {
        console.log(`❌ Customer not found: ${customerId}`);
        return null;
      }

      // Generate AI insights
      const aiInsights = await this.generateAIInsights(customerId, {
        basicData,
        communicationData,
        equipmentData,
        financialData,
        analyticsData
      });

      // Calculate data completeness
      const dataCompleteness = this.calculateDataCompleteness({
        basicData,
        communicationData,
        equipmentData,
        financialData,
        analyticsData
      });

      // Assemble unified profile
      const unifiedProfile: UnifiedCustomerProfile = {
        customerId,
        basicInfo: basicData.basicInfo,
        contactInfo: basicData.contactInfo,
        analytics: analyticsData.analytics,
        healthScore: analyticsData.healthScore,
        lifecycleStage: analyticsData.lifecycleStage,
        communications: communicationData.summary,
        emailIntelligence: communicationData.emailIntelligence,
        transcriptions: communicationData.transcriptions,
        equipment: equipmentData.equipment,
        serviceHistory: equipmentData.serviceHistory,
        financial: financialData,
        documents: await this.getDocumentSummary(customerId),
        timeline: timelineData,
        recentActivity: await this.getRecentActivity(customerId),
        aiInsights,
        lastUpdated: new Date(),
        dataCompleteness
      };

      console.log(`✅ Unified profile assembled for customer: ${customerId} (${dataCompleteness}% complete)`);
      return unifiedProfile;

    } catch (error) {
      console.error(`❌ Error fetching unified profile for customer ${customerId}:`, error);
      throw new Error(`Failed to fetch unified customer profile: ${error.message}`);
    }
  }

  /**
   * 📊 Get customer basic data with optimized query
   * Leverages Prisma relationships for efficient data fetching
   */
  private static async getCustomerBasicData(customerId: string) {
    try {
      const customer = await prisma.customer.findUnique({
        where: { id: customerId },
        include: {
          user: true, // Include user relationship
        }
      });

      if (!customer) return null;

      return {
        basicInfo: {
          name: customer.name,
          type: (customer.type as 'RESIDENTIAL' | 'COMMERCIAL' | 'INDUSTRIAL') || 'RESIDENTIAL',
          status: 'ACTIVE' as const, // Would come from customer status field
          accountNumber: customer.id,
          taxId: customer.taxId,
          website: customer.website,
          industry: customer.industry,
          companySize: customer.companySize,
          createdAt: customer.createdAt,
          updatedAt: customer.updatedAt,
        },
        contactInfo: {
          primaryAddress: {
            street: customer.address || '',
            city: customer.city || '',
            state: customer.state || '',
            zipCode: customer.postalCode || '',
            country: customer.country || 'USA',
          },
          primaryPhone: customer.phone || '',
          email: customer.email || '',
          preferredContactMethod: 'EMAIL' as const,
        }
      };
    } catch (error) {
      console.error(`❌ Error fetching basic data for customer ${customerId}:`, error);
      throw error;
    }
  }

  /**
   * 📧 Get comprehensive communication data
   * Aggregates emails, calls, and other communications with AI analysis
   */
  private static async getCommunicationData(customerId: string) {
    try {
      // Get communication records from Prisma
      const communications = await prisma.communication.findMany({
        where: { customerId },
        orderBy: { timestamp: 'desc' },
        include: {
          attachments: true,
        }
      });

      // Get email intelligence data from GoBackend-Kratos
      const emailData = await goBackendBridge.executeWithRetry(
        'GET',
        `/api/customers/${customerId}/emails`,
        null,
        { timeout: 10000 }
      );

      // Get transcription data from GoBackend-Kratos
      const transcriptionData = await goBackendBridge.executeWithRetry(
        'GET',
        `/api/customers/${customerId}/transcriptions`,
        null,
        { timeout: 10000 }
      );

      // Process communication summary
      const emailCount = communications.filter(c => c.channel === 'EMAIL').length;
      const phoneCallCount = communications.filter(c => c.channel === 'PHONE').length;
      const smsCount = communications.filter(c => c.channel === 'SMS').length;
      const inPersonCount = communications.filter(c => c.channel === 'IN_PERSON').length;

      const lastCommunication = communications[0];
      const totalCommunications = communications.length;

      // Calculate average response time (simplified)
      const averageResponseTime = this.calculateAverageResponseTime(communications);

      // Calculate communication frequency (per month)
      const communicationFrequency = this.calculateCommunicationFrequency(communications);

      // Determine preferred channel
      const channelCounts = {
        EMAIL: emailCount,
        PHONE: phoneCallCount,
        SMS: smsCount,
        IN_PERSON: inPersonCount
      };
      const preferredChannel = Object.entries(channelCounts)
        .sort(([,a], [,b]) => b - a)[0]?.[0] || 'EMAIL';

      // Calculate sentiment trend
      const sentimentTrend = this.calculateSentimentTrend(communications);

      return {
        summary: {
          totalCommunications,
          emailCount,
          phoneCallCount,
          smsCount,
          inPersonCount,
          lastCommunicationDate: lastCommunication?.timestamp,
          averageResponseTime,
          communicationFrequency,
          preferredChannel,
          sentimentTrend,
        },
        emailIntelligence: {
          totalEmails: emailData?.totalEmails || emailCount,
          inboundEmails: emailData?.inboundEmails || 0,
          outboundEmails: emailData?.outboundEmails || 0,
          averageSentiment: emailData?.averageSentiment || 0,
          urgentEmails: emailData?.urgentEmails || 0,
          unreadEmails: emailData?.unreadEmails || 0,
          lastEmailDate: emailData?.lastEmailDate ? new Date(emailData.lastEmailDate) : undefined,
          topCategories: emailData?.topCategories || [],
          aiConfidenceAverage: emailData?.aiConfidenceAverage || 0,
        },
        transcriptions: {
          totalTranscriptions: transcriptionData?.totalTranscriptions || 0,
          totalCallDuration: transcriptionData?.totalCallDuration || 0,
          averageCallDuration: transcriptionData?.averageCallDuration || 0,
          lastCallDate: transcriptionData?.lastCallDate ? new Date(transcriptionData.lastCallDate) : undefined,
          callFrequency: transcriptionData?.callFrequency || 0,
          topTopics: transcriptionData?.topTopics || [],
          sentimentTrend: transcriptionData?.sentimentTrend || [],
        }
      };
    } catch (error) {
      console.error(`❌ Error fetching communication data for customer ${customerId}:`, error);
      // Return default values on error
      return {
        summary: {
          totalCommunications: 0,
          emailCount: 0,
          phoneCallCount: 0,
          smsCount: 0,
          inPersonCount: 0,
          averageResponseTime: 0,
          communicationFrequency: 0,
          preferredChannel: 'EMAIL',
          sentimentTrend: 'STABLE' as const,
        },
        emailIntelligence: {
          totalEmails: 0,
          inboundEmails: 0,
          outboundEmails: 0,
          averageSentiment: 0,
          urgentEmails: 0,
          unreadEmails: 0,
          topCategories: [],
          aiConfidenceAverage: 0,
        },
        transcriptions: {
          totalTranscriptions: 0,
          totalCallDuration: 0,
          averageCallDuration: 0,
          callFrequency: 0,
          topTopics: [],
          sentimentTrend: [],
        }
      };
    }
  }

  /**
   * 🔧 Get equipment and service data
   * Comprehensive equipment tracking with service history
   */
  private static async getEquipmentData(customerId: string) {
    try {
      // Get devices/equipment from Prisma
      const devices = await prisma.device.findMany({
        where: { customerId },
        include: {
          serviceOrders: {
            include: {
              invoices: true,
              serviceReports: true,
            },
            orderBy: { createdAt: 'desc' }
          },
          telemetry: {
            orderBy: { timestamp: 'desc' },
            take: 10
          },
          predictions: {
            orderBy: { predictionDate: 'desc' },
            take: 5
          }
        }
      });

      // Get additional equipment data from GoBackend-Kratos
      const equipmentData = await goBackendBridge.executeWithRetry(
        'GET',
        `/api/customers/${customerId}/equipment`,
        null,
        { timeout: 10000 }
      );

      // Process equipment summary
      const equipment: EquipmentSummary[] = devices.map(device => {
        const serviceOrders = device.serviceOrders || [];
        const totalServiceCost = serviceOrders.reduce((sum, so) => {
          return sum + (so.invoices?.reduce((invSum, inv) => invSum + (inv.totalAmount || 0), 0) || 0);
        }, 0);

        const lastService = serviceOrders[0];
        const nextServiceDue = this.calculateNextServiceDue(device, serviceOrders);

        return {
          id: device.id,
          type: device.type || 'HVAC_UNIT',
          brand: device.brand || '',
          model: device.model || '',
          serialNumber: device.serialNumber || '',
          installationDate: device.installationDate,
          warrantyExpiry: device.warrantyExpiry,
          lastServiceDate: lastService?.createdAt,
          nextServiceDue,
          status: device.status as 'ACTIVE' | 'INACTIVE' | 'NEEDS_REPLACEMENT' || 'ACTIVE',
          maintenanceCompliance: this.calculateMaintenanceCompliance(device, serviceOrders),
          serviceCount: serviceOrders.length,
          totalServiceCost,
        };
      });

      // Calculate service history summary
      const allServiceOrders = devices.flatMap(d => d.serviceOrders || []);
      const serviceHistory: ServiceHistorySummary = {
        totalServices: allServiceOrders.length,
        maintenanceServices: allServiceOrders.filter(so => so.type === 'MAINTENANCE').length,
        repairServices: allServiceOrders.filter(so => so.type === 'REPAIR').length,
        installationServices: allServiceOrders.filter(so => so.type === 'INSTALLATION').length,
        emergencyServices: allServiceOrders.filter(so => so.priority === 'URGENT').length,
        averageServiceCost: allServiceOrders.length > 0 ?
          allServiceOrders.reduce((sum, so) => sum + (so.invoices?.[0]?.totalAmount || 0), 0) / allServiceOrders.length : 0,
        totalServiceCost: allServiceOrders.reduce((sum, so) => sum + (so.invoices?.[0]?.totalAmount || 0), 0),
        onTimePercentage: this.calculateOnTimePercentage(allServiceOrders),
        customerSatisfactionAverage: this.calculateSatisfactionAverage(allServiceOrders),
        lastServiceDate: allServiceOrders[0]?.createdAt,
        upcomingServices: this.getUpcomingServices(allServiceOrders),
      };

      return {
        equipment,
        serviceHistory
      };
    } catch (error) {
      console.error(`❌ Error fetching equipment data for customer ${customerId}:`, error);
      return {
        equipment: [],
        serviceHistory: {
          totalServices: 0,
          maintenanceServices: 0,
          repairServices: 0,
          installationServices: 0,
          emergencyServices: 0,
          averageServiceCost: 0,
          totalServiceCost: 0,
          onTimePercentage: 0,
          customerSatisfactionAverage: 0,
          upcomingServices: [],
        }
      };
    }
  }

  /**
   * 💰 Get comprehensive financial data
   * Aggregates invoices, payments, and financial metrics
   */
  private static async getFinancialData(customerId: string): Promise<FinancialSummary> {
    try {
      // Get invoices from Prisma
      const invoices = await prisma.invoice.findMany({
        where: { customerId },
        orderBy: { createdAt: 'desc' },
        include: {
          serviceOrder: true,
        }
      });

      // Get additional financial data from GoBackend-Kratos
      const financialData = await goBackendBridge.executeWithRetry(
        'GET',
        `/api/customers/${customerId}/financial`,
        null,
        { timeout: 10000 }
      );

      // Calculate financial metrics
      const totalRevenue = invoices
        .filter(inv => inv.paymentStatus === 'PAID')
        .reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);

      const totalInvoices = invoices.length;
      const paidInvoices = invoices.filter(inv => inv.paymentStatus === 'PAID').length;
      const pendingInvoices = invoices.filter(inv => inv.paymentStatus === 'PENDING').length;
      const overdueInvoices = invoices.filter(inv => {
        const dueDate = inv.dueDate;
        return dueDate && new Date() > dueDate && inv.paymentStatus !== 'PAID';
      }).length;

      // Calculate average payment time
      const paidInvoicesWithDates = invoices.filter(inv =>
        inv.paymentStatus === 'PAID' && inv.paidAt && inv.createdAt
      );
      const averagePaymentTime = paidInvoicesWithDates.length > 0 ?
        paidInvoicesWithDates.reduce((sum, inv) => {
          const paymentTime = inv.paidAt!.getTime() - inv.createdAt.getTime();
          return sum + (paymentTime / (1000 * 60 * 60 * 24)); // Convert to days
        }, 0) / paidInvoicesWithDates.length : 0;

      // Calculate current balance (unpaid invoices)
      const currentBalance = invoices
        .filter(inv => inv.paymentStatus !== 'PAID')
        .reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);

      // Get last payment date
      const lastPaymentDate = paidInvoicesWithDates
        .sort((a, b) => (b.paidAt?.getTime() || 0) - (a.paidAt?.getTime() || 0))[0]?.paidAt;

      // Calculate payment methods frequency
      const paymentMethods = invoices
        .filter(inv => inv.paymentMethod)
        .reduce((acc, inv) => {
          const method = inv.paymentMethod!;
          acc[method] = (acc[method] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

      const paymentMethodsArray = Object.entries(paymentMethods)
        .map(([method, frequency]) => ({ method, frequency }));

      // Calculate monthly revenue trend (last 12 months)
      const monthlyRevenueTrend = this.calculateMonthlyRevenueTrend(invoices);

      return {
        totalRevenue,
        totalInvoices,
        paidInvoices,
        pendingInvoices,
        overdueInvoices,
        averagePaymentTime,
        creditLimit: financialData?.creditLimit,
        currentBalance,
        lastPaymentDate,
        paymentMethods: paymentMethodsArray,
        monthlyRevenueTrend,
      };
    } catch (error) {
      console.error(`❌ Error fetching financial data for customer ${customerId}:`, error);
      return {
        totalRevenue: 0,
        totalInvoices: 0,
        paidInvoices: 0,
        pendingInvoices: 0,
        overdueInvoices: 0,
        averagePaymentTime: 0,
        currentBalance: 0,
        paymentMethods: [],
        monthlyRevenueTrend: [],
      };
    }
  }

  /**
   * 📊 Get analytics data with health scoring and lifecycle stage
   */
  private static async getAnalyticsData(customerId: string) {
    try {
      // Get customer with related data for analytics
      const customer = await prisma.customer.findUnique({
        where: { id: customerId },
        include: {
          serviceOrders: {
            include: { invoices: true },
            orderBy: { createdAt: 'desc' }
          },
          invoices: {
            orderBy: { createdAt: 'desc' }
          },
          devices: true,
          communications: {
            orderBy: { timestamp: 'desc' }
          }
        }
      });

      if (!customer) {
        throw new Error('Customer not found');
      }

      // Calculate basic analytics
      const totalServiceOrders = customer.serviceOrders.length;
      const totalRevenue = customer.invoices
        .filter(inv => inv.paymentStatus === 'PAID')
        .reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);
      const averageJobValue = totalServiceOrders > 0 ? totalRevenue / totalServiceOrders : 0;

      // Calculate customer lifetime value (simplified)
      const monthsSinceFirst = customer.serviceOrders.length > 0 ?
        Math.max(1, Math.floor((Date.now() - customer.serviceOrders[customer.serviceOrders.length - 1].createdAt.getTime()) / (1000 * 60 * 60 * 24 * 30))) : 1;
      const monthlyValue = totalRevenue / monthsSinceFirst;
      const projectedLifetimeMonths = 36; // 3 years
      const customerLifetimeValue = monthlyValue * projectedLifetimeMonths;

      // Get last service date
      const lastServiceDate = customer.serviceOrders[0]?.createdAt;

      // Calculate next scheduled service (simplified)
      const nextScheduledService = this.calculateNextScheduledService(customer.serviceOrders);

      // Calculate satisfaction score (from service orders)
      const satisfactionScore = this.calculateSatisfactionScore(customer.serviceOrders);

      // Determine loyalty tier
      const loyaltyTier = this.determineLoyaltyTier(totalRevenue, totalServiceOrders);

      // Calculate churn risk
      const churnRisk = this.calculateChurnRisk(customer);

      // Calculate months as customer
      const monthsAsCustomer = Math.floor((Date.now() - customer.createdAt.getTime()) / (1000 * 60 * 60 * 24 * 30));

      // Calculate health score
      const healthScore = this.calculateHealthScore(customer);

      // Determine lifecycle stage
      const lifecycleStage = this.determineLifecycleStage(customer);

      return {
        analytics: {
          totalServiceOrders,
          totalRevenue,
          averageJobValue,
          customerLifetimeValue,
          lastServiceDate,
          nextScheduledService,
          satisfactionScore,
          loyaltyTier,
          churnRisk,
          acquisitionDate: customer.createdAt,
          monthsAsCustomer,
        },
        healthScore,
        lifecycleStage,
      };
    } catch (error) {
      console.error(`❌ Error fetching analytics data for customer ${customerId}:`, error);
      throw error;
    }
  }

  /**
   * 📅 Get customer timeline data
   * Aggregates all customer interactions into chronological timeline
   */
  private static async getTimelineData(customerId: string): Promise<CustomerTimelineEvent[]> {
    try {
      const timeline: CustomerTimelineEvent[] = [];

      // Get service orders
      const serviceOrders = await prisma.serviceOrder.findMany({
        where: { customerId },
        orderBy: { createdAt: 'desc' },
        take: 50
      });

      // Get communications
      const communications = await prisma.communication.findMany({
        where: { customerId },
        orderBy: { timestamp: 'desc' },
        take: 50
      });

      // Get invoices
      const invoices = await prisma.invoice.findMany({
        where: { customerId },
        orderBy: { createdAt: 'desc' },
        take: 50
      });

      // Add service order events
      serviceOrders.forEach(so => {
        timeline.push({
          id: `service_${so.id}`,
          type: 'SERVICE',
          title: `Service Order: ${so.title}`,
          description: so.description || '',
          timestamp: so.createdAt,
          relatedEntityId: so.id,
          relatedEntityType: 'ServiceOrder',
          importance: so.priority === 'URGENT' ? 'CRITICAL' : 'MEDIUM',
          metadata: {
            status: so.status,
            priority: so.priority,
            type: so.type
          }
        });
      });

      // Add communication events
      communications.forEach(comm => {
        timeline.push({
          id: `comm_${comm.id}`,
          type: 'COMMUNICATION',
          title: `${comm.channel}: ${comm.subject}`,
          description: comm.content.substring(0, 100) + '...',
          timestamp: comm.timestamp,
          relatedEntityId: comm.id,
          relatedEntityType: 'Communication',
          importance: comm.channel === 'PHONE' ? 'HIGH' : 'MEDIUM',
          metadata: {
            channel: comm.channel,
            direction: comm.direction,
            read: comm.read
          }
        });
      });

      // Add payment events
      invoices.filter(inv => inv.paymentStatus === 'PAID' && inv.paidAt).forEach(inv => {
        timeline.push({
          id: `payment_${inv.id}`,
          type: 'PAYMENT',
          title: `Payment Received: $${inv.totalAmount}`,
          description: `Invoice ${inv.invoiceNumber} paid`,
          timestamp: inv.paidAt!,
          relatedEntityId: inv.id,
          relatedEntityType: 'Invoice',
          importance: 'MEDIUM',
          metadata: {
            amount: inv.totalAmount,
            method: inv.paymentMethod
          }
        });
      });

      // Sort timeline by timestamp (most recent first)
      timeline.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      return timeline.slice(0, 100); // Return last 100 events
    } catch (error) {
      console.error(`❌ Error fetching timeline data for customer ${customerId}:`, error);
      return [];
    }
  }

  /**
   * 🤖 Generate AI insights and predictions
   * Uses Bielik V3 for comprehensive customer analysis
   */
  private static async generateAIInsights(customerId: string, data: any): Promise<CustomerAIInsights> {
    try {
      // Prepare data for AI analysis
      const analysisData = {
        customerId,
        totalRevenue: data.analyticsData?.analytics?.totalRevenue || 0,
        serviceHistory: data.equipmentData?.serviceHistory || {},
        communicationPattern: data.communicationData?.summary || {},
        financialBehavior: data.financialData || {},
        equipmentCount: data.equipmentData?.equipment?.length || 0,
      };

      // Generate AI insights using Bielik V3
      const aiResponse = await bielikService.generateResponse({
        prompt: this.buildCustomerAnalysisPrompt(analysisData),
        model: 'bielik-v3',
        temperature: 0.3,
        maxTokens: 2000,
      });

      // Parse AI response (simplified - would need proper JSON parsing)
      const insights = this.parseAIInsightsResponse(aiResponse.content);

      return {
        churnPrediction: insights.churnPrediction || {
          probability: this.calculateChurnProbability(data),
          factors: ['Low communication frequency', 'Overdue payments'],
          recommendation: 'Increase engagement through personalized communication'
        },
        upsellOpportunities: insights.upsellOpportunities || [],
        maintenanceRecommendations: insights.maintenanceRecommendations || [],
        communicationInsights: insights.communicationInsights || {
          preferredTime: 'Business hours',
          responsePattern: 'Quick responder',
          sentimentAnalysis: 'Generally positive'
        },
        lastAnalyzed: new Date()
      };
    } catch (error) {
      console.error(`❌ Error generating AI insights for customer ${customerId}:`, error);
      // Return default insights on error
      return {
        churnPrediction: {
          probability: 0,
          factors: [],
          recommendation: 'Insufficient data for analysis'
        },
        upsellOpportunities: [],
        maintenanceRecommendations: [],
        communicationInsights: {
          preferredTime: 'Unknown',
          responsePattern: 'Unknown',
          sentimentAnalysis: 'Neutral'
        },
        lastAnalyzed: new Date()
      };
    }
  }

  /**
   * 📄 Get document summary
   */
  private static async getDocumentSummary(customerId: string): Promise<DocumentSummary> {
    try {
      // Get documents from various sources
      const attachments = await prisma.attachment.findMany({
        where: {
          OR: [
            { serviceOrder: { customerId } },
            { communication: { customerId } },
            { invoice: { customerId } }
          ]
        }
      });

      // Categorize documents
      const contracts = attachments.filter(att => att.type?.includes('contract')).length;
      const invoices = attachments.filter(att => att.type?.includes('invoice')).length;
      const serviceReports = attachments.filter(att => att.type?.includes('report')).length;
      const warranties = attachments.filter(att => att.type?.includes('warranty')).length;
      const photos = attachments.filter(att => att.type?.includes('image')).length;
      const other = attachments.length - (contracts + invoices + serviceReports + warranties + photos);

      // Calculate storage used (simplified)
      const storageUsed = attachments.reduce((sum, att) => sum + (att.size || 0), 0) / (1024 * 1024); // Convert to MB

      const lastDocumentAdded = attachments
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0]?.createdAt;

      return {
        totalDocuments: attachments.length,
        contracts,
        invoices,
        serviceReports,
        warranties,
        photos,
        other,
        lastDocumentAdded,
        storageUsed
      };
    } catch (error) {
      console.error(`❌ Error fetching document summary for customer ${customerId}:`, error);
      return {
        totalDocuments: 0,
        contracts: 0,
        invoices: 0,
        serviceReports: 0,
        warranties: 0,
        photos: 0,
        other: 0,
        storageUsed: 0
      };
    }
  }

  /**
   * 🔄 Get recent activity summary
   */
  private static async getRecentActivity(customerId: string): Promise<RecentActivitySummary> {
    try {
      // Get recent communications
      const recentCommunication = await prisma.communication.findFirst({
        where: { customerId },
        orderBy: { timestamp: 'desc' }
      });

      // Get recent service request
      const recentServiceRequest = await prisma.serviceOrder.findFirst({
        where: { customerId },
        orderBy: { createdAt: 'desc' }
      });

      // Get recent payment
      const recentPayment = await prisma.invoice.findFirst({
        where: {
          customerId,
          paymentStatus: 'PAID',
          paidAt: { not: null }
        },
        orderBy: { paidAt: 'desc' }
      });

      return {
        lastCommunication: recentCommunication?.timestamp,
        lastServiceRequest: recentServiceRequest?.createdAt,
        lastPayment: recentPayment?.paidAt || undefined,
        recentChanges: [] // Would track field changes with audit log
      };
    } catch (error) {
      console.error(`❌ Error fetching recent activity for customer ${customerId}:`, error);
      return {
        recentChanges: []
      };
    }
  }

  // ========================================
  // 🛠️ UTILITY METHODS
  // ========================================

  /**
   * Calculate data completeness percentage
   */
  private static calculateDataCompleteness(data: any): number {
    let totalFields = 0;
    let completedFields = 0;

    // Check basic info completeness
    const basicInfo = data.basicData?.basicInfo;
    if (basicInfo) {
      totalFields += 8;
      if (basicInfo.name) completedFields++;
      if (basicInfo.type) completedFields++;
      if (basicInfo.status) completedFields++;
      if (basicInfo.accountNumber) completedFields++;
      if (basicInfo.taxId) completedFields++;
      if (basicInfo.website) completedFields++;
      if (basicInfo.industry) completedFields++;
      if (basicInfo.companySize) completedFields++;
    }

    // Check contact info completeness
    const contactInfo = data.basicData?.contactInfo;
    if (contactInfo) {
      totalFields += 6;
      if (contactInfo.primaryAddress?.street) completedFields++;
      if (contactInfo.primaryAddress?.city) completedFields++;
      if (contactInfo.primaryPhone) completedFields++;
      if (contactInfo.email) completedFields++;
      if (contactInfo.preferredContactMethod) completedFields++;
      if (contactInfo.businessHours) completedFields++;
    }

    // Check communication data
    if (data.communicationData?.summary?.totalCommunications > 0) {
      totalFields += 2;
      completedFields += 2;
    }

    // Check equipment data
    if (data.equipmentData?.equipment?.length > 0) {
      totalFields += 2;
      completedFields += 2;
    }

    // Check financial data
    if (data.financialData?.totalInvoices > 0) {
      totalFields += 2;
      completedFields += 2;
    }

    return totalFields > 0 ? Math.round((completedFields / totalFields) * 100) : 0;
  }

  /**
   * Calculate average response time from communications
   */
  private static calculateAverageResponseTime(communications: any[]): number {
    // Simplified calculation - would need more sophisticated logic
    const responseTimes = communications
      .filter(c => c.direction === 'OUTBOUND')
      .map(c => 2); // Assume 2 hours average response time

    return responseTimes.length > 0 ?
      responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;
  }

  /**
   * Calculate communication frequency per month
   */
  private static calculateCommunicationFrequency(communications: any[]): number {
    if (communications.length === 0) return 0;

    const now = new Date();
    const threeMonthsAgo = new Date(now.getTime() - 3 * 30 * 24 * 60 * 60 * 1000);
    const recentComms = communications.filter(c => c.timestamp > threeMonthsAgo);

    return (recentComms.length / 3); // Communications per month
  }

  /**
   * Calculate sentiment trend from communications
   */
  private static calculateSentimentTrend(communications: any[]): 'IMPROVING' | 'STABLE' | 'DECLINING' {
    if (communications.length < 5) return 'STABLE';

    // Simplified sentiment trend calculation
    const recent = communications.slice(0, 5);
    const older = communications.slice(5, 10);

    const recentPositive = recent.filter(c => c.sentiment === 'POSITIVE').length;
    const olderPositive = older.filter(c => c.sentiment === 'POSITIVE').length;

    if (recentPositive > olderPositive) return 'IMPROVING';
    if (recentPositive < olderPositive) return 'DECLINING';
    return 'STABLE';
  }

  /**
   * Calculate next service due date
   */
  private static calculateNextServiceDue(device: any, serviceOrders: any[]): Date | undefined {
    if (serviceOrders.length === 0) return undefined;

    const lastService = serviceOrders[0];
    const maintenanceInterval = 365; // Days (1 year default)

    return new Date(lastService.createdAt.getTime() + maintenanceInterval * 24 * 60 * 60 * 1000);
  }

  /**
   * Calculate maintenance compliance percentage
   */
  private static calculateMaintenanceCompliance(device: any, serviceOrders: any[]): number {
    // Simplified calculation - would need more sophisticated logic
    const expectedServices = Math.floor((Date.now() - device.createdAt.getTime()) / (365 * 24 * 60 * 60 * 1000));
    const actualServices = serviceOrders.filter(so => so.type === 'MAINTENANCE').length;

    return expectedServices > 0 ? Math.min(100, (actualServices / expectedServices) * 100) : 100;
  }

  /**
   * Calculate on-time service percentage
   */
  private static calculateOnTimePercentage(serviceOrders: any[]): number {
    const scheduledServices = serviceOrders.filter(so => so.scheduledDate);
    if (scheduledServices.length === 0) return 100;

    const onTimeServices = scheduledServices.filter(so => {
      if (!so.completedAt || !so.scheduledDate) return false;
      return so.completedAt <= so.scheduledDate;
    });

    return (onTimeServices.length / scheduledServices.length) * 100;
  }

  /**
   * Calculate customer satisfaction average
   */
  private static calculateSatisfactionAverage(serviceOrders: any[]): number {
    const ratedServices = serviceOrders.filter(so => so.customerRating);
    if (ratedServices.length === 0) return 0;

    const totalRating = ratedServices.reduce((sum, so) => sum + so.customerRating, 0);
    return totalRating / ratedServices.length;
  }

  /**
   * Get upcoming services
   */
  private static getUpcomingServices(serviceOrders: any[]): Array<{
    id: string;
    scheduledDate: Date;
    type: string;
    equipment: string;
  }> {
    const now = new Date();
    return serviceOrders
      .filter(so => so.scheduledDate && so.scheduledDate > now && so.status !== 'COMPLETED')
      .slice(0, 5)
      .map(so => ({
        id: so.id,
        scheduledDate: so.scheduledDate,
        type: so.type || 'SERVICE',
        equipment: so.device?.type || 'Equipment'
      }));
  }

  /**
   * Calculate monthly revenue trend
   */
  private static calculateMonthlyRevenueTrend(invoices: any[]): Array<{ month: string; revenue: number }> {
    const monthlyData: Record<string, number> = {};

    invoices
      .filter(inv => inv.paymentStatus === 'PAID' && inv.paidAt)
      .forEach(inv => {
        const month = inv.paidAt.toISOString().substring(0, 7); // YYYY-MM
        monthlyData[month] = (monthlyData[month] || 0) + (inv.totalAmount || 0);
      });

    return Object.entries(monthlyData)
      .sort(([a], [b]) => a.localeCompare(b))
      .slice(-12) // Last 12 months
      .map(([month, revenue]) => ({ month, revenue }));
  }

  /**
   * Calculate next scheduled service
   */
  private static calculateNextScheduledService(serviceOrders: any[]): Date | undefined {
    const upcomingServices = serviceOrders.filter(so =>
      so.scheduledDate && so.scheduledDate > new Date() && so.status !== 'COMPLETED'
    );

    if (upcomingServices.length === 0) return undefined;

    return upcomingServices.sort((a, b) => a.scheduledDate.getTime() - b.scheduledDate.getTime())[0].scheduledDate;
  }

  /**
   * Calculate satisfaction score from service orders
   */
  private static calculateSatisfactionScore(serviceOrders: any[]): number | undefined {
    const ratedOrders = serviceOrders.filter(so => so.customerRating);
    if (ratedOrders.length === 0) return undefined;

    const totalRating = ratedOrders.reduce((sum, so) => sum + so.customerRating, 0);
    return totalRating / ratedOrders.length;
  }

  /**
   * Determine loyalty tier based on revenue and service count
   */
  private static determineLoyaltyTier(totalRevenue: number, serviceCount: number): 'BRONZE' | 'SILVER' | 'GOLD' | 'PLATINUM' {
    if (totalRevenue > 50000 || serviceCount > 20) return 'PLATINUM';
    if (totalRevenue > 25000 || serviceCount > 10) return 'GOLD';
    if (totalRevenue > 10000 || serviceCount > 5) return 'SILVER';
    return 'BRONZE';
  }

  /**
   * Calculate churn risk percentage
   */
  private static calculateChurnRisk(customer: any): number {
    let riskScore = 0;

    // Check last communication date
    const lastComm = customer.communications?.[0];
    if (lastComm) {
      const daysSinceLastComm = (Date.now() - lastComm.timestamp.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceLastComm > 90) riskScore += 30;
      else if (daysSinceLastComm > 60) riskScore += 20;
      else if (daysSinceLastComm > 30) riskScore += 10;
    }

    // Check overdue invoices
    const overdueInvoices = customer.invoices?.filter(inv =>
      inv.dueDate && new Date() > inv.dueDate && inv.paymentStatus !== 'PAID'
    ).length || 0;
    riskScore += overdueInvoices * 15;

    // Check service frequency
    const recentServices = customer.serviceOrders?.filter(so =>
      so.createdAt > new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000)
    ).length || 0;
    if (recentServices === 0) riskScore += 25;

    return Math.min(100, riskScore);
  }

  /**
   * Calculate comprehensive health score
   */
  private static calculateHealthScore(customer: any): CustomerHealthScore {
    const serviceFrequency = this.calculateServiceFrequencyScore(customer.serviceOrders);
    const paymentHistory = this.calculatePaymentHistoryScore(customer.invoices);
    const communicationResponsiveness = this.calculateCommunicationScore(customer.communications);
    const contractCompliance = this.calculateComplianceScore(customer);
    const referralActivity = this.calculateReferralScore(customer);

    const overall = Math.round((serviceFrequency + paymentHistory + communicationResponsiveness + contractCompliance + referralActivity) / 5);

    return {
      overall,
      serviceFrequency,
      paymentHistory,
      communicationResponsiveness,
      contractCompliance,
      referralActivity,
      lastCalculated: new Date()
    };
  }

  /**
   * Determine customer lifecycle stage
   */
  private static determineLifecycleStage(customer: any): CustomerLifecycleStage {
    const serviceCount = customer.serviceOrders?.length || 0;
    const monthsAsCustomer = Math.floor((Date.now() - customer.createdAt.getTime()) / (1000 * 60 * 60 * 24 * 30));
    const lastServiceDays = customer.serviceOrders?.[0] ?
      Math.floor((Date.now() - customer.serviceOrders[0].createdAt.getTime()) / (1000 * 60 * 60 * 24)) : 999;

    let current: CustomerLifecycleStage['current'];

    if (serviceCount === 0) {
      current = 'LEAD';
    } else if (serviceCount === 1 && monthsAsCustomer < 3) {
      current = 'NEW_CUSTOMER';
    } else if (lastServiceDays > 365) {
      current = 'CHURNED';
    } else if (lastServiceDays > 180) {
      current = 'AT_RISK';
    } else if (serviceCount > 10 && monthsAsCustomer > 12) {
      current = 'LOYAL_CUSTOMER';
    } else {
      current = 'ACTIVE_CUSTOMER';
    }

    return {
      current,
      stageChangedAt: new Date(), // Would track actual stage changes
      daysInCurrentStage: 30 // Would calculate from stage change history
    };
  }

  // Additional utility methods for health score calculation
  private static calculateServiceFrequencyScore(serviceOrders: any[]): number {
    if (!serviceOrders || serviceOrders.length === 0) return 50;

    const recentServices = serviceOrders.filter(so =>
      so.createdAt > new Date(Date.now() - 12 * 30 * 24 * 60 * 60 * 1000)
    ).length;

    return Math.min(100, recentServices * 20);
  }

  private static calculatePaymentHistoryScore(invoices: any[]): number {
    if (!invoices || invoices.length === 0) return 100;

    const paidOnTime = invoices.filter(inv =>
      inv.paymentStatus === 'PAID' &&
      (!inv.dueDate || inv.paidAt <= inv.dueDate)
    ).length;

    return (paidOnTime / invoices.length) * 100;
  }

  private static calculateCommunicationScore(communications: any[]): number {
    if (!communications || communications.length === 0) return 50;

    const recentComms = communications.filter(c =>
      c.timestamp > new Date(Date.now() - 3 * 30 * 24 * 60 * 60 * 1000)
    );

    return Math.min(100, recentComms.length * 10);
  }

  private static calculateComplianceScore(customer: any): number {
    // Simplified compliance calculation
    return 85; // Would calculate based on contract terms, maintenance schedules, etc.
  }

  private static calculateReferralScore(customer: any): number {
    // Simplified referral calculation
    return 70; // Would calculate based on actual referrals made
  }

  /**
   * Build AI analysis prompt for customer insights
   */
  private static buildCustomerAnalysisPrompt(data: any): string {
    return `
Analyze this HVAC customer data and provide insights:

Customer ID: ${data.customerId}
Total Revenue: $${data.totalRevenue}
Equipment Count: ${data.equipmentCount}
Service History: ${JSON.stringify(data.serviceHistory)}
Communication Pattern: ${JSON.stringify(data.communicationPattern)}
Financial Behavior: ${JSON.stringify(data.financialBehavior)}

Please provide analysis in JSON format:
{
  "churnPrediction": {
    "probability": 0-100,
    "factors": ["factor1", "factor2"],
    "recommendation": "recommendation text"
  },
  "upsellOpportunities": [
    {
      "service": "service name",
      "probability": 0-100,
      "estimatedValue": amount,
      "reasoning": "why this opportunity exists"
    }
  ],
  "maintenanceRecommendations": [
    {
      "equipment": "equipment type",
      "recommendation": "what to do",
      "urgency": "LOW|MEDIUM|HIGH",
      "estimatedCost": amount
    }
  ],
  "communicationInsights": {
    "preferredTime": "time preference",
    "responsePattern": "response behavior",
    "sentimentAnalysis": "overall sentiment"
  }
}
    `;
  }

  /**
   * Parse AI insights response
   */
  private static parseAIInsightsResponse(response: string): any {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.error('Error parsing AI insights response:', error);
    }

    return {}; // Return empty object if parsing fails
  }

  /**
   * Calculate churn probability for AI insights
   */
  private static calculateChurnProbability(data: any): number {
    // Simplified churn probability calculation
    let probability = 0;

    const totalRevenue = data.analyticsData?.analytics?.totalRevenue || 0;
    const lastServiceDays = data.equipmentData?.serviceHistory?.lastServiceDate ?
      Math.floor((Date.now() - new Date(data.equipmentData.serviceHistory.lastServiceDate).getTime()) / (1000 * 60 * 60 * 24)) : 999;

    if (totalRevenue < 1000) probability += 20;
    if (lastServiceDays > 365) probability += 40;
    if (lastServiceDays > 180) probability += 20;

    return Math.min(100, probability);
  }
}