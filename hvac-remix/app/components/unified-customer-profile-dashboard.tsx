/**
 * 🎯 UNIFIED CUSTOMER PROFILE DASHBOARD
 *
 * Comprehensive customer profile dashboard that displays all customer data
 * in a unified, intuitive interface. Leverages the power of relational
 * database aggregation to provide complete 360° customer view.
 *
 * Features:
 * - Complete customer data aggregation display
 * - Real-time updates and synchronization
 * - AI-powered insights and recommendations
 * - Interactive timeline and activity tracking
 * - Responsive design with cosmic-level UX
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Progress } from "~/components/ui/progress";
import { Separator } from "~/components/ui/separator";
import {
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  MessageSquare,
  <PERSON>ch,
  <PERSON><PERSON><PERSON>t,
  BarChart3,
  <PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  Download,
  Edit,
  Star,
  Shield,
  Zap
} from 'lucide-react';

export interface UnifiedCustomerProfileDashboardProps {
  customerId: string;
  onActionClick?: (action: string, data?: any) => void;
  showActions?: boolean;
  refreshInterval?: number; // milliseconds
}

export interface UnifiedCustomerProfile {
  customerId: string;
  basicInfo: {
    name: string;
    type: 'RESIDENTIAL' | 'COMMERCIAL' | 'INDUSTRIAL';
    status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
    accountNumber: string;
    createdAt: Date;
    updatedAt: Date;
  };
  contactInfo: {
    primaryAddress: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
    primaryPhone: string;
    email: string;
    preferredContactMethod: 'EMAIL' | 'PHONE' | 'SMS';
  };
  analytics: {
    totalServiceOrders: number;
    totalRevenue: number;
    averageJobValue: number;
    customerLifetimeValue: number;
    lastServiceDate?: Date;
    loyaltyTier: 'BRONZE' | 'SILVER' | 'GOLD' | 'PLATINUM';
    churnRisk: number;
    monthsAsCustomer: number;
  };
  healthScore: {
    overall: number;
    serviceFrequency: number;
    paymentHistory: number;
    communicationResponsiveness: number;
    contractCompliance: number;
    referralActivity: number;
    lastCalculated: Date;
  };
  communications: {
    totalCommunications: number;
    emailCount: number;
    phoneCallCount: number;
    lastCommunicationDate?: Date;
    preferredChannel: string;
    sentimentTrend: 'IMPROVING' | 'STABLE' | 'DECLINING';
  };
  equipment: Array<{
    id: string;
    type: string;
    brand: string;
    model: string;
    status: 'ACTIVE' | 'INACTIVE' | 'NEEDS_REPLACEMENT';
    lastServiceDate?: Date;
    nextServiceDue?: Date;
    maintenanceCompliance: number;
  }>;
  financial: {
    totalRevenue: number;
    totalInvoices: number;
    paidInvoices: number;
    pendingInvoices: number;
    overdueInvoices: number;
    currentBalance: number;
    lastPaymentDate?: Date;
  };
  aiInsights: {
    churnPrediction: {
      probability: number;
      factors: string[];
      recommendation: string;
    };
    upsellOpportunities: Array<{
      service: string;
      probability: number;
      estimatedValue: number;
      reasoning: string;
    }>;
    maintenanceRecommendations: Array<{
      equipment: string;
      recommendation: string;
      urgency: 'LOW' | 'MEDIUM' | 'HIGH';
      estimatedCost: number;
    }>;
  };
  timeline: Array<{
    id: string;
    type: 'SERVICE' | 'COMMUNICATION' | 'PAYMENT' | 'EQUIPMENT' | 'DOCUMENT' | 'SYSTEM';
    title: string;
    description: string;
    timestamp: Date;
    importance: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  }>;
  dataCompleteness: number;
  lastUpdated: Date;
}

export function UnifiedCustomerProfileDashboard({
  customerId,
  onActionClick,
  showActions = true,
  refreshInterval = 300000 // 5 minutes
}: UnifiedCustomerProfileDashboardProps) {
  const [profile, setProfile] = useState<UnifiedCustomerProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch unified customer profile
  const fetchProfile = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) setRefreshing(true);

      const response = await fetch(`/api/unified-customer-profile/${customerId}`);
      const data = await response.json();

      if (data.success) {
        setProfile(data.profile);
        setError(null);
      } else {
        setError(data.error || 'Failed to fetch customer profile');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Error fetching customer profile:', err);
    } finally {
      setLoading(false);
      if (showRefreshIndicator) setRefreshing(false);
    }
  };

  // Initial load and auto-refresh
  useEffect(() => {
    fetchProfile();

    const interval = setInterval(() => {
      fetchProfile();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [customerId, refreshInterval]);

  // Handle action clicks
  const handleAction = (action: string, data?: any) => {
    if (onActionClick) {
      onActionClick(action, data);
    }
  };

  // Manual refresh
  const handleRefresh = () => {
    fetchProfile(true);
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading unified customer profile...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !profile) {
    return (
      <Card className="border-destructive">
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-destructive mb-2">Error Loading Profile</h3>
            <p className="text-muted-foreground mb-4">{error || 'Customer profile not found'}</p>
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get status color based on customer status and health
  const getStatusColor = () => {
    if (profile.basicInfo.status === 'INACTIVE') return 'bg-gray-500';
    if (profile.basicInfo.status === 'SUSPENDED') return 'bg-red-500';
    if (profile.healthScore.overall >= 80) return 'bg-green-500';
    if (profile.healthScore.overall >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  // Get loyalty tier icon
  const getLoyaltyIcon = () => {
    switch (profile.analytics.loyaltyTier) {
      case 'PLATINUM': return <Star className="h-4 w-4 text-purple-500" />;
      case 'GOLD': return <Star className="h-4 w-4 text-yellow-500" />;
      case 'SILVER': return <Star className="h-4 w-4 text-gray-400" />;
      default: return <Star className="h-4 w-4 text-amber-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
            <h1 className="text-2xl font-bold">{profile.basicInfo.name}</h1>
            <Badge variant="outline" className="flex items-center space-x-1">
              {getLoyaltyIcon()}
              <span>{profile.analytics.loyaltyTier}</span>
            </Badge>
          </div>
        </div>

        {showActions && (
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              {profile.dataCompleteness}% Complete
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleAction('export_profile')}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleAction('edit_profile')}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </div>
        )}
      </div>

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Health Score</p>
                <p className="text-2xl font-bold">{profile.healthScore.overall}%</p>
              </div>
              <Shield className="h-8 w-8 text-green-500" />
            </div>
            <Progress value={profile.healthScore.overall} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">${profile.analytics.totalRevenue.toLocaleString()}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Churn Risk</p>
                <p className="text-2xl font-bold">{profile.analytics.churnRisk}%</p>
              </div>
              <AlertTriangle className={`h-8 w-8 ${profile.analytics.churnRisk > 50 ? 'text-red-500' : 'text-yellow-500'}`} />
            </div>
            <Progress value={profile.analytics.churnRisk} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Equipment</p>
                <p className="text-2xl font-bold">{profile.equipment.length}</p>
              </div>
              <Wrench className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="communications">Communications</TabsTrigger>
          <TabsTrigger value="equipment">Equipment</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span>Basic Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Customer Type</p>
                    <p className="font-semibold">{profile.basicInfo.type}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Account Number</p>
                    <p className="font-semibold">{profile.basicInfo.accountNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Customer Since</p>
                    <p className="font-semibold">{profile.analytics.monthsAsCustomer} months</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                    <Badge variant={profile.basicInfo.status === 'ACTIVE' ? 'default' : 'secondary'}>
                      {profile.basicInfo.status}
                    </Badge>
                  </div>
                </div>

                <Separator />

                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.contactInfo.email}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.contactInfo.primaryPhone}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {profile.contactInfo.primaryAddress.street}, {profile.contactInfo.primaryAddress.city}, {profile.contactInfo.primaryAddress.state} {profile.contactInfo.primaryAddress.zipCode}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Health Score Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>Health Score Breakdown</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Service Frequency</span>
                      <span>{profile.healthScore.serviceFrequency}%</span>
                    </div>
                    <Progress value={profile.healthScore.serviceFrequency} />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Payment History</span>
                      <span>{profile.healthScore.paymentHistory}%</span>
                    </div>
                    <Progress value={profile.healthScore.paymentHistory} />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Communication</span>
                      <span>{profile.healthScore.communicationResponsiveness}%</span>
                    </div>
                    <Progress value={profile.healthScore.communicationResponsiveness} />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Contract Compliance</span>
                      <span>{profile.healthScore.contractCompliance}%</span>
                    </div>
                    <Progress value={profile.healthScore.contractCompliance} />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Referral Activity</span>
                      <span>{profile.healthScore.referralActivity}%</span>
                    </div>
                    <Progress value={profile.healthScore.referralActivity} />
                  </div>
                </div>

                <Separator />

                <div className="text-center">
                  <p className="text-2xl font-bold text-primary">{profile.healthScore.overall}%</p>
                  <p className="text-sm text-muted-foreground">Overall Health Score</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Last calculated: {new Date(profile.healthScore.lastCalculated).toLocaleDateString()}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Analytics Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Analytics Summary</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-primary">{profile.analytics.totalServiceOrders}</p>
                  <p className="text-sm text-muted-foreground">Service Orders</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">${profile.analytics.averageJobValue.toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">Avg Job Value</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">${profile.analytics.customerLifetimeValue.toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">Lifetime Value</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{profile.communications.totalCommunications}</p>
                  <p className="text-sm text-muted-foreground">Communications</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">{profile.equipment.length}</p>
                  <p className="text-sm text-muted-foreground">Equipment</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">{profile.financial.totalInvoices}</p>
                  <p className="text-sm text-muted-foreground">Invoices</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Communications Tab */}
        <TabsContent value="communications" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MessageSquare className="h-5 w-5" />
                  <span>Communication Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{profile.communications.emailCount}</p>
                    <p className="text-sm text-muted-foreground">Emails</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{profile.communications.phoneCallCount}</p>
                    <p className="text-sm text-muted-foreground">Phone Calls</p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Preferred Channel</span>
                    <Badge variant="outline">{profile.communications.preferredChannel}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Sentiment Trend</span>
                    <Badge variant={
                      profile.communications.sentimentTrend === 'IMPROVING' ? 'default' :
                      profile.communications.sentimentTrend === 'DECLINING' ? 'destructive' : 'secondary'
                    }>
                      {profile.communications.sentimentTrend}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Last Communication</span>
                    <span className="text-sm">
                      {profile.communications.lastCommunicationDate ?
                        new Date(profile.communications.lastCommunicationDate).toLocaleDateString() :
                        'No recent communication'
                      }
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {profile.timeline.slice(0, 5).map((event) => (
                    <div key={event.id} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        event.importance === 'CRITICAL' ? 'bg-red-500' :
                        event.importance === 'HIGH' ? 'bg-orange-500' :
                        event.importance === 'MEDIUM' ? 'bg-yellow-500' : 'bg-gray-500'
                      }`} />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium">{event.title}</p>
                        <p className="text-xs text-muted-foreground">{event.description}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(event.timestamp).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Equipment Tab */}
        <TabsContent value="equipment" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {profile.equipment.map((equipment) => (
              <Card key={equipment.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center space-x-2">
                      <Wrench className="h-5 w-5" />
                      <span>{equipment.type}</span>
                    </span>
                    <Badge variant={
                      equipment.status === 'ACTIVE' ? 'default' :
                      equipment.status === 'NEEDS_REPLACEMENT' ? 'destructive' : 'secondary'
                    }>
                      {equipment.status}
                    </Badge>
                  </CardTitle>
                  <CardDescription>
                    {equipment.brand} {equipment.model}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Maintenance Compliance</span>
                      <span>{equipment.maintenanceCompliance}%</span>
                    </div>
                    <Progress value={equipment.maintenanceCompliance} />
                  </div>

                  <Separator />

                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Last Service</span>
                      <span>
                        {equipment.lastServiceDate ?
                          new Date(equipment.lastServiceDate).toLocaleDateString() :
                          'No service recorded'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Next Service Due</span>
                      <span>
                        {equipment.nextServiceDue ?
                          new Date(equipment.nextServiceDue).toLocaleDateString() :
                          'Not scheduled'
                        }
                      </span>
                    </div>
                  </div>

                  {equipment.nextServiceDue && new Date(equipment.nextServiceDue) < new Date() && (
                    <div className="flex items-center space-x-2 text-red-600 text-sm">
                      <AlertTriangle className="h-4 w-4" />
                      <span>Service overdue</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {profile.equipment.length === 0 && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-muted-foreground">
                  <Wrench className="h-8 w-8 mx-auto mb-4" />
                  <p>No equipment registered for this customer</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Financial Tab */}
        <TabsContent value="financial" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5" />
                  <span>Financial Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">${profile.financial.totalRevenue.toLocaleString()}</p>
                    <p className="text-sm text-muted-foreground">Total Revenue</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{profile.financial.totalInvoices}</p>
                    <p className="text-sm text-muted-foreground">Total Invoices</p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Paid Invoices</span>
                    <span className="text-sm font-medium text-green-600">{profile.financial.paidInvoices}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Pending Invoices</span>
                    <span className="text-sm font-medium text-yellow-600">{profile.financial.pendingInvoices}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Overdue Invoices</span>
                    <span className="text-sm font-medium text-red-600">{profile.financial.overdueInvoices}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Current Balance</span>
                    <span className="text-sm font-medium">${profile.financial.currentBalance.toLocaleString()}</span>
                  </div>
                </div>

                {profile.financial.lastPaymentDate && (
                  <div className="pt-2 border-t">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Last Payment</span>
                      <span>{new Date(profile.financial.lastPaymentDate).toLocaleDateString()}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Payment Performance</span>
                      <span>{Math.round((profile.financial.paidInvoices / profile.financial.totalInvoices) * 100)}%</span>
                    </div>
                    <Progress value={(profile.financial.paidInvoices / profile.financial.totalInvoices) * 100} />
                  </div>

                  {profile.financial.overdueInvoices > 0 && (
                    <div className="flex items-center space-x-2 text-red-600 text-sm">
                      <AlertTriangle className="h-4 w-4" />
                      <span>{profile.financial.overdueInvoices} overdue invoice(s) require attention</span>
                    </div>
                  )}

                  {profile.financial.currentBalance > 0 && (
                    <div className="flex items-center space-x-2 text-yellow-600 text-sm">
                      <Clock className="h-4 w-4" />
                      <span>Outstanding balance: ${profile.financial.currentBalance.toLocaleString()}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* AI Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Churn Prediction */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Brain className="h-5 w-5" />
                  <span>Churn Prediction</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <p className="text-3xl font-bold text-red-600">{profile.aiInsights.churnPrediction.probability}%</p>
                  <p className="text-sm text-muted-foreground">Churn Risk</p>
                </div>

                <Progress value={profile.aiInsights.churnPrediction.probability} className="h-2" />

                <div className="space-y-2">
                  <p className="text-sm font-medium">Risk Factors:</p>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    {profile.aiInsights.churnPrediction.factors.map((factor, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <div className="w-1 h-1 bg-red-500 rounded-full" />
                        <span>{factor}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="pt-2 border-t">
                  <p className="text-sm font-medium mb-1">Recommendation:</p>
                  <p className="text-sm text-muted-foreground">{profile.aiInsights.churnPrediction.recommendation}</p>
                </div>
              </CardContent>
            </Card>

            {/* Upsell Opportunities */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Upsell Opportunities</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {profile.aiInsights.upsellOpportunities.length > 0 ? (
                    profile.aiInsights.upsellOpportunities.map((opportunity, index) => (
                      <div key={index} className="border rounded-lg p-3">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium">{opportunity.service}</h4>
                          <Badge variant="outline">{opportunity.probability}% likely</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{opportunity.reasoning}</p>
                        <p className="text-sm font-medium text-green-600">
                          Estimated value: ${opportunity.estimatedValue.toLocaleString()}
                        </p>
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground">
                      <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                      <p>No upsell opportunities identified</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Maintenance Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Wrench className="h-5 w-5" />
                <span>Maintenance Recommendations</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {profile.aiInsights.maintenanceRecommendations.length > 0 ? (
                  profile.aiInsights.maintenanceRecommendations.map((recommendation, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium">{recommendation.equipment}</h4>
                        <Badge variant={
                          recommendation.urgency === 'HIGH' ? 'destructive' :
                          recommendation.urgency === 'MEDIUM' ? 'default' : 'secondary'
                        }>
                          {recommendation.urgency} Priority
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{recommendation.recommendation}</p>
                      <p className="text-sm font-medium">
                        Estimated cost: ${recommendation.estimatedCost.toLocaleString()}
                      </p>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-muted-foreground">
                    <CheckCircle className="h-8 w-8 mx-auto mb-2" />
                    <p>No maintenance recommendations at this time</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Timeline Tab */}
        <TabsContent value="timeline" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span>Customer Timeline</span>
              </CardTitle>
              <CardDescription>
                Complete chronological history of all customer interactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {profile.timeline.map((event) => (
                  <div key={event.id} className="flex items-start space-x-4 pb-4 border-b last:border-b-0">
                    <div className={`w-3 h-3 rounded-full mt-2 ${
                      event.importance === 'CRITICAL' ? 'bg-red-500' :
                      event.importance === 'HIGH' ? 'bg-orange-500' :
                      event.importance === 'MEDIUM' ? 'bg-yellow-500' : 'bg-gray-500'
                    }`} />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="text-sm font-medium">{event.title}</h4>
                        <Badge variant="outline" className="text-xs">
                          {event.type}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">{event.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(event.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}

                {profile.timeline.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    <Clock className="h-8 w-8 mx-auto mb-4" />
                    <p>No timeline events available</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Footer */}
      <div className="text-center text-sm text-muted-foreground">
        <p>Last updated: {new Date(profile.lastUpdated).toLocaleString()}</p>
        <p>Data completeness: {profile.dataCompleteness}% • Powered by relational database aggregation</p>
      </div>
    </div>
  );
}
